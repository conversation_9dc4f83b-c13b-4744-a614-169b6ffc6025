"""
Core models for the application.
"""

import json
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model

User = get_user_model()


class BaseModel(models.Model):
    """
    Abstract base model with common fields.
    """

    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        abstract = True


class SystemConfiguration(BaseModel):
    """
    Model to store system-wide configuration settings.
    """

    key = models.CharField(_("key"), max_length=255, unique=True)
    value = models.TextField(_("value"))
    description = models.TextField(_("description"), blank=True)
    is_active = models.BooleanField(_("is active"), default=True)

    class Meta:
        verbose_name = _("System Configuration")
        verbose_name_plural = _("System Configurations")
        db_table = "core_system_configurations"

    def __str__(self):
        return f"{self.key}: {self.value[:50]}"


class AuditLog(BaseModel):
    """
    Model to store audit logs for important actions.
    """

    user = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="audit_logs",
    )
    action = models.CharField(_("action"), max_length=100)
    resource_type = models.CharField(_("resource type"), max_length=100)
    resource_id = models.CharField(_("resource ID"), max_length=100, blank=True)
    details = models.JSONField(_("details"), default=dict)
    ip_address = models.GenericIPAddressField(_("IP address"), blank=True, null=True)
    user_agent = models.TextField(_("user agent"), blank=True)

    class Meta:
        verbose_name = _("Audit Log")
        verbose_name_plural = _("Audit Logs")
        db_table = "core_audit_logs"
        indexes = [
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["action", "created_at"]),
            models.Index(fields=["resource_type", "resource_id"]),
        ]

    def __str__(self):
        user_str = self.user.email if self.user else "Anonymous"
        return f"{user_str} - {self.action} on {self.resource_type}"


# 可视化代码生成工具相关模型


class ModelProject(BaseModel):
    """
    模型项目 - 用于组织和管理模型设计
    """

    name = models.CharField(_("project name"), max_length=100, unique=True)
    description = models.TextField(_("description"), blank=True)
    app_name = models.CharField(_("app name"), max_length=50)
    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="model_projects",
        verbose_name=_("owner"),
    )
    is_active = models.BooleanField(_("is active"), default=True)
    version = models.CharField(_("version"), max_length=20, default="1.0.0")

    # 项目配置
    config = models.JSONField(_("project config"), default=dict, blank=True)

    class Meta:
        verbose_name = _("Model Project")
        verbose_name_plural = _("Model Projects")
        db_table = "core_model_projects"
        ordering = ["-updated_at"]

    def __str__(self):
        return f"{self.name} ({self.app_name})"

    def get_models_count(self):
        """获取项目中的模型数量"""
        return self.model_designs.count()

    def export_to_yaml(self):
        """导出为 YAML 配置格式"""
        from .services import ModelProjectService

        return ModelProjectService.export_project_to_yaml(self)


class ModelDesign(BaseModel):
    """
    模型设计 - 存储单个模型的设计信息
    """

    project = models.ForeignKey(
        ModelProject,
        on_delete=models.CASCADE,
        related_name="model_designs",
        verbose_name=_("project"),
    )
    name = models.CharField(_("model name"), max_length=100)
    description = models.TextField(_("description"), blank=True)

    # 模型选项
    verbose_name = models.CharField(_("verbose name"), max_length=100, blank=True)
    verbose_name_plural = models.CharField(
        _("verbose name plural"), max_length=100, blank=True
    )
    db_table = models.CharField(_("database table"), max_length=100, blank=True)
    ordering = models.JSONField(_("ordering"), default=list, blank=True)

    # 索引和约束
    indexes = models.JSONField(_("indexes"), default=list, blank=True)
    constraints = models.JSONField(_("constraints"), default=list, blank=True)
    permissions = models.JSONField(_("permissions"), default=list, blank=True)

    # 位置信息（用于可视化布局）
    position_x = models.IntegerField(_("position x"), default=0)
    position_y = models.IntegerField(_("position y"), default=0)

    class Meta:
        verbose_name = _("Model Design")
        verbose_name_plural = _("Model Designs")
        db_table = "core_model_designs"
        unique_together = [["project", "name"]]
        ordering = ["name"]

    def __str__(self):
        return f"{self.project.name}.{self.name}"

    def get_fields_count(self):
        """获取字段数量"""
        return self.field_designs.count()

    def get_relationships(self):
        """获取关系字段"""
        return self.field_designs.filter(field_type__in=["fk", "m2m", "o2o"])


class FieldDesign(BaseModel):
    """
    字段设计 - 存储模型字段的设计信息
    """

    FIELD_TYPE_CHOICES = [
        ("str", _("String")),
        ("text", _("Text")),
        ("int", _("Integer")),
        ("float", _("Float")),
        ("decimal", _("Decimal")),
        ("bool", _("Boolean")),
        ("date", _("Date")),
        ("datetime", _("DateTime")),
        ("time", _("Time")),
        ("email", _("Email")),
        ("url", _("URL")),
        ("slug", _("Slug")),
        ("uuid", _("UUID")),
        ("json", _("JSON")),
        ("file", _("File")),
        ("image", _("Image")),
        ("choices", _("Choices")),
        ("fk", _("Foreign Key")),
        ("m2m", _("Many to Many")),
        ("o2o", _("One to One")),
    ]

    ON_DELETE_CHOICES = [
        ("CASCADE", _("CASCADE")),
        ("PROTECT", _("PROTECT")),
        ("SET_NULL", _("SET_NULL")),
        ("SET_DEFAULT", _("SET_DEFAULT")),
        ("DO_NOTHING", _("DO_NOTHING")),
    ]

    model_design = models.ForeignKey(
        ModelDesign,
        on_delete=models.CASCADE,
        related_name="field_designs",
        verbose_name=_("model design"),
    )
    name = models.CharField(_("field name"), max_length=100)
    field_type = models.CharField(
        _("field type"), max_length=20, choices=FIELD_TYPE_CHOICES
    )
    description = models.TextField(_("description"), blank=True)

    # 基本字段属性
    null = models.BooleanField(_("null"), default=False)
    blank = models.BooleanField(_("blank"), default=False)
    unique = models.BooleanField(_("unique"), default=False)
    db_index = models.BooleanField(_("db index"), default=False)

    # 字符串字段属性
    max_length = models.IntegerField(_("max length"), null=True, blank=True)

    # 数字字段属性
    max_digits = models.IntegerField(_("max digits"), null=True, blank=True)
    decimal_places = models.IntegerField(_("decimal places"), null=True, blank=True)

    # 默认值
    default_value = models.TextField(_("default value"), blank=True)

    # 验证器
    validators = models.JSONField(_("validators"), default=list, blank=True)

    # 帮助文本和显示名称
    help_text = models.TextField(_("help text"), blank=True)
    verbose_name = models.CharField(_("verbose name"), max_length=100, blank=True)

    # 关系字段属性
    related_model = models.CharField(_("related model"), max_length=100, blank=True)
    on_delete = models.CharField(
        _("on delete"), max_length=20, choices=ON_DELETE_CHOICES, blank=True
    )
    related_name = models.CharField(_("related name"), max_length=100, blank=True)

    # 文件字段属性
    upload_to = models.CharField(_("upload to"), max_length=200, blank=True)

    # 选择字段
    choices = models.JSONField(_("choices"), default=list, blank=True)

    # 字段顺序
    order = models.IntegerField(_("order"), default=0)

    class Meta:
        verbose_name = _("Field Design")
        verbose_name_plural = _("Field Designs")
        db_table = "core_field_designs"
        unique_together = [["model_design", "name"]]
        ordering = ["order", "name"]

    def __str__(self):
        return f"{self.model_design.name}.{self.name} ({self.field_type})"

    def get_field_definition(self):
        """获取字段定义字典"""
        definition = {
            "type": self.field_type,
            "null": self.null,
            "blank": self.blank,
            "unique": self.unique,
            "db_index": self.db_index,
        }

        if self.max_length:
            definition["max_length"] = self.max_length

        if self.max_digits:
            definition["max_digits"] = self.max_digits

        if self.decimal_places:
            definition["decimal_places"] = self.decimal_places

        if self.default_value:
            definition["default"] = self.default_value

        if self.help_text:
            definition["help_text"] = self.help_text

        if self.verbose_name:
            definition["verbose_name"] = self.verbose_name

        if self.validators:
            definition["validators"] = self.validators

        if self.field_type in ["fk", "m2m", "o2o"]:
            if self.related_model:
                definition["to"] = self.related_model
            if self.on_delete:
                definition["on_delete"] = self.on_delete
            if self.related_name:
                definition["related_name"] = self.related_name

        if self.field_type in ["file", "image"] and self.upload_to:
            definition["upload_to"] = self.upload_to

        if self.field_type == "choices" and self.choices:
            definition["choices"] = self.choices

        return definition


class CodeGenerationHistory(BaseModel):
    """
    代码生成历史记录
    """

    STATUS_CHOICES = [
        ("pending", _("Pending")),
        ("running", _("Running")),
        ("success", _("Success")),
        ("failed", _("Failed")),
    ]

    project = models.ForeignKey(
        ModelProject,
        on_delete=models.CASCADE,
        related_name="generation_history",
        verbose_name=_("project"),
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="code_generations",
        verbose_name=_("user"),
    )

    status = models.CharField(
        _("status"), max_length=20, choices=STATUS_CHOICES, default="pending"
    )

    # 生成选项
    generate_full = models.BooleanField(_("generate full"), default=False)
    generate_frontend = models.BooleanField(_("generate frontend"), default=False)
    output_directory = models.CharField(
        _("output directory"), max_length=200, default="apps"
    )

    # 生成结果
    generated_files = models.JSONField(_("generated files"), default=list, blank=True)
    error_message = models.TextField(_("error message"), blank=True)
    execution_time = models.FloatField(_("execution time"), null=True, blank=True)

    # 生成的配置快照
    config_snapshot = models.JSONField(_("config snapshot"), default=dict, blank=True)

    class Meta:
        verbose_name = _("Code Generation History")
        verbose_name_plural = _("Code Generation History")
        db_table = "core_code_generation_history"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.project.name} - {self.status} ({self.created_at})"


class ModelTemplate(BaseModel):
    """
    模型模板 - 预定义的模型模板
    """

    CATEGORY_CHOICES = [
        ("blog", _("Blog")),
        ("ecommerce", _("E-commerce")),
        ("cms", _("CMS")),
        ("user", _("User Management")),
        ("common", _("Common")),
        ("custom", _("Custom")),
    ]

    name = models.CharField(_("template name"), max_length=100, unique=True)
    description = models.TextField(_("description"))
    category = models.CharField(_("category"), max_length=20, choices=CATEGORY_CHOICES)

    # 模板配置
    template_config = models.JSONField(_("template config"), default=dict)

    # 预览图
    preview_image = models.URLField(_("preview image"), blank=True)

    # 使用统计
    usage_count = models.IntegerField(_("usage count"), default=0)

    is_active = models.BooleanField(_("is active"), default=True)
    is_featured = models.BooleanField(_("is featured"), default=False)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_templates",
        verbose_name=_("created by"),
    )

    class Meta:
        verbose_name = _("Model Template")
        verbose_name_plural = _("Model Templates")
        db_table = "core_model_templates"
        ordering = ["-is_featured", "-usage_count", "name"]

    def __str__(self):
        return f"{self.name} ({self.category})"

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=["usage_count"])


class FieldPreset(BaseModel):
    """
    字段预设 - 常用字段组合
    """

    name = models.CharField(_("preset name"), max_length=100, unique=True)
    description = models.TextField(_("description"))

    # 字段配置
    fields_config = models.JSONField(_("fields config"), default=list)

    # 分类标签
    tags = models.JSONField(_("tags"), default=list, blank=True)

    usage_count = models.IntegerField(_("usage count"), default=0)
    is_active = models.BooleanField(_("is active"), default=True)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_presets",
        verbose_name=_("created by"),
    )

    class Meta:
        verbose_name = _("Field Preset")
        verbose_name_plural = _("Field Presets")
        db_table = "core_field_presets"
        ordering = ["-usage_count", "name"]

    def __str__(self):
        return self.name

    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.save(update_fields=["usage_count"])
