<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: #f0f2f5;
        }
        .dashboard {
            min-height: 100vh;
        }
        .header {
            background: #001529;
            color: white;
            padding: 0 24px;
            display: flex;
            align-items: center;
            height: 64px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 24px;
        }
        .nav-menu {
            flex: 1;
        }
        .content {
            padding: 24px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 32px;
        }
        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .hero-subtitle {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 32px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        .feature-card {
            background: white;
            padding: 32px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
        .feature-icon {
            font-size: 48px;
            color: #409EFF;
            margin-bottom: 16px;
        }
        .feature-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #303133;
        }
        .feature-description {
            color: #606266;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard">
            <!-- 头部导航 -->
            <div class="header">
                <div class="logo">Django Ninja CMS</div>
                <div class="nav-menu">
                    <el-menu mode="horizontal" background-color="#001529" text-color="#fff" active-text-color="#409EFF">
                        <el-menu-item index="1">首页</el-menu-item>
                        <el-menu-item index="2">
                            <a href="/model-designer/" style="color: inherit; text-decoration: none;">模型设计器</a>
                        </el-menu-item>
                        <el-menu-item index="3">文档</el-menu-item>
                    </el-menu>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="content">
                <!-- 英雄区域 -->
                <div class="hero-section">
                    <div class="hero-title">{{ title }}</div>
                    <div class="hero-subtitle">{{ description }}</div>
                    <el-button type="primary" size="large" @click="goToDesigner">
                        <i class="el-icon-edit"></i> 开始设计
                    </el-button>
                </div>

                <!-- 功能特性 -->
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-edit-outline"></i>
                        </div>
                        <div class="feature-title">可视化模型编辑器</div>
                        <div class="feature-description">
                            通过直观的 Web 界面设计 Django 模型，支持拖拽操作，实时预览模型关系图。
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-connection"></i>
                        </div>
                        <div class="feature-title">智能关系管理</div>
                        <div class="feature-description">
                            自动识别和管理模型间的外键、多对多关系，提供可视化的关系图表。
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-cpu"></i>
                        </div>
                        <div class="feature-title">一键代码生成</div>
                        <div class="feature-description">
                            自动生成完整的 Django 应用代码，包括模型、API、Admin 和前端组件。
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-document"></i>
                        </div>
                        <div class="feature-title">配置导入导出</div>
                        <div class="feature-description">
                            支持 YAML 配置文件的导入导出，方便版本控制和团队协作。
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-collection"></i>
                        </div>
                        <div class="feature-title">模板和预设</div>
                        <div class="feature-description">
                            提供丰富的模型模板和字段预设，快速创建常见的业务模型。
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="el-icon-view"></i>
                        </div>
                        <div class="feature-title">实时预览</div>
                        <div class="feature-description">
                            实时预览生成的代码和配置，支持语法高亮和格式化显示。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    // 数据可以在这里定义
                };
            },
            
            methods: {
                goToDesigner() {
                    window.location.href = '/model-designer/';
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
