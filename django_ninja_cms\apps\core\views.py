"""
Core views for the application.
"""
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View


@login_required
def model_designer(request):
    """
    可视化模型设计器页面
    """
    return render(request, 'core/model_designer.html')


class HealthCheckView(View):
    """
    健康检查视图
    """
    
    def get(self, request):
        """
        健康检查端点
        """
        return JsonResponse({
            'status': 'healthy',
            'message': 'Core application is running'
        })


@method_decorator(csrf_exempt, name='dispatch')
class WebhookView(View):
    """
    Webhook 处理视图
    """
    
    def post(self, request):
        """
        处理 webhook 请求
        """
        # 这里可以添加 webhook 处理逻辑
        # 比如处理代码生成完成的通知等
        return JsonResponse({
            'status': 'received',
            'message': 'Webhook processed successfully'
        })


def dashboard(request):
    """
    仪表板页面
    """
    context = {
        'title': '可视化代码生成工具',
        'description': '基于 Web 界面的 Django 模型设计和代码生成工具'
    }
    return render(request, 'core/dashboard.html', context)
