# 可视化模型设计器 - 快速开始

## 🚀 5分钟快速上手

### 第一步：数据库迁移

```bash
# 创建迁移文件
python manage.py makemigrations model_designer

# 应用迁移
python manage.py migrate

# 创建初始模板和预设
python manage.py create_initial_templates
```

### 第二步：启动服务

```bash
# 启动开发服务器
python manage.py runserver
```

### 第三步：访问界面

打开浏览器访问：
- **仪表板**: http://localhost:8000/model-designer/dashboard/
- **设计器**: http://localhost:8000/model-designer/

## 📝 创建第一个项目

### 1. 新建项目
1. 点击"新建项目"按钮
2. 填写信息：
   - 项目名称：`我的博客`
   - 应用名称：`blog`
   - 描述：`个人博客系统`
3. 点击"创建"

### 2. 添加模型
1. 点击"添加模型"
2. 填写：
   - 模型名称：`Post`
   - 显示名称：`博客文章`
3. 点击"添加"

### 3. 添加字段
为 Post 模型添加以下字段：

| 字段名 | 类型 | 选项 | 说明 |
|--------|------|------|------|
| title | String | max_length=200 | 文章标题 |
| content | Text | - | 文章内容 |
| status | Choices | draft/published | 发布状态 |
| created_at | DateTime | auto_now_add=True | 创建时间 |

### 4. 生成代码
1. 点击"生成代码"按钮
2. 选择"完整功能"
3. 点击"确定"

## 🎯 使用模板快速创建

### 博客模板
1. 在模板列表中选择"博客系统"
2. 点击"使用模板"
3. 自动创建包含 Post、Category、Tag 的完整博客系统

### 电商模板
1. 选择"电商系统"模板
2. 自动创建 Product、Category、Brand、Order 等模型

## 🔧 常用字段预设

### 时间戳字段组
- `created_at`: 创建时间
- `updated_at`: 更新时间

### SEO字段组
- `seo_title`: SEO标题
- `seo_description`: SEO描述
- `seo_keywords`: SEO关键词

### 状态控制字段组
- `is_active`: 启用状态
- `is_deleted`: 删除状态

## 📤 配置导入导出

### 导出配置
```bash
# 在界面中点击"导出配置"，或使用API
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/model-designer/projects/1/export-yaml/
```

### 导入配置
```yaml
app_name: blog
models:
  Post:
    fields:
      title:
        type: str
        max_length: 200
        verbose_name: 标题
      content:
        type: text
        verbose_name: 内容
    options:
      verbose_name: 博客文章
      ordering: ['-created_at']
```

## 🛠️ 生成的文件结构

代码生成后会创建以下文件：

```
apps/blog/
├── __init__.py
├── models.py          # Django 模型
├── admin.py           # Admin 配置
├── api.py             # API 端点
├── schemas.py         # Pydantic 模式
├── views.py           # 视图函数
├── urls.py            # URL 配置
├── apps.py            # 应用配置
├── migrations/        # 数据库迁移
└── templates/         # 模板文件（可选）
```

## 🔍 API 使用示例

### 获取项目列表
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/model-designer/projects/
```

### 创建新项目
```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的项目",
    "app_name": "myapp",
    "description": "项目描述"
  }' \
  http://localhost:8000/api/model-designer/projects/
```

### 生成代码
```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "full": true,
    "with_frontend": false,
    "output_dir": "apps"
  }' \
  http://localhost:8000/api/model-designer/projects/1/generate/
```

## 🎨 界面操作技巧

### 模型卡片操作
- **拖拽移动**: 直接拖拽调整模型位置
- **点击选择**: 点击模型卡片进行选择
- **双击编辑**: 双击模型名称快速编辑

### 字段管理
- **快速添加**: 使用字段预设快速添加常用字段
- **批量操作**: 选择多个字段进行批量操作
- **拖拽排序**: 拖拽字段调整显示顺序

### 关系设置
- **外键关系**: 选择 Foreign Key 类型，指定关联模型
- **多对多**: 选择 Many to Many 类型，配置中间表
- **一对一**: 选择 One to One 类型，建立唯一关联

## ⚠️ 注意事项

### 命名规范
- **模型名**: 使用 PascalCase，如 `BlogPost`
- **字段名**: 使用 snake_case，如 `created_at`
- **应用名**: 使用小写字母，如 `blog`

### 字段类型选择
- **文本内容**: 短文本用 String，长文本用 Text
- **数字**: 整数用 Integer，小数用 Decimal
- **时间**: 日期用 Date，日期时间用 DateTime
- **关系**: 一对多用 Foreign Key，多对多用 Many to Many

### 性能考虑
- **索引**: 为经常查询的字段添加 db_index
- **外键**: 合理设置 on_delete 行为
- **默认值**: 为必填字段设置合理的默认值

## 🆘 常见问题

### Q: 生成的代码在哪里？
A: 默认在 `apps/` 目录下，以应用名称命名的文件夹中。

### Q: 如何修改已生成的代码？
A: 可以直接编辑生成的文件，或在设计器中修改后重新生成。

### Q: 支持哪些字段类型？
A: 支持所有 Django 标准字段类型，包括 String、Text、Integer、Boolean、DateTime、Foreign Key 等。

### Q: 可以导入现有模型吗？
A: 目前支持通过 YAML 配置导入，未来版本将支持直接从现有 Django 模型导入。

### Q: 如何备份设计？
A: 使用"导出配置"功能将设计保存为 YAML 文件，可用于备份和版本控制。

---

🎉 **恭喜！** 您已经掌握了可视化模型设计器的基本使用方法。

更多高级功能请参考 [完整使用指南](./可视化模型设计器使用指南.md)。
