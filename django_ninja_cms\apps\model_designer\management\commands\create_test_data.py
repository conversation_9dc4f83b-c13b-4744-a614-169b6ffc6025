"""
创建测试数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.model_designer.models import ModelProject, ModelDesign, FieldDesign

User = get_user_model()


class Command(BaseCommand):
    help = '创建模型设计器的测试数据'

    def handle(self, *args, **options):
        self.stdout.write('开始创建测试数据...')
        
        # 创建或获取测试用户
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'testuser',
                'first_name': 'Test',
                'last_name': 'User',
                'is_active': True
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
            self.stdout.write(f'创建测试用户: {user.email}')
        else:
            self.stdout.write(f'使用现有用户: {user.email}')

        # 创建测试项目
        project, created = ModelProject.objects.get_or_create(
            name='博客系统',
            defaults={
                'app_name': 'blog',
                'description': '一个简单的博客系统示例',
                'owner': user,
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'创建测试项目: {project.name}')
        else:
            self.stdout.write(f'使用现有项目: {project.name}')

        # 创建文章模型
        article_model, created = ModelDesign.objects.get_or_create(
            project=project,
            name='Article',
            defaults={
                'verbose_name': '文章',
                'verbose_name_plural': '文章',
                'description': '博客文章模型',
                'position_x': 100,
                'position_y': 100
            }
        )
        if created:
            self.stdout.write(f'创建模型: {article_model.name}')

            # 创建文章字段
            fields_data = [
                {'name': 'title', 'field_type': 'CharField', 'verbose_name': '标题', 'max_length': 200},
                {'name': 'content', 'field_type': 'TextField', 'verbose_name': '内容'},
                {'name': 'author', 'field_type': 'ForeignKey', 'verbose_name': '作者', 'related_model': 'User'},
                {'name': 'created_at', 'field_type': 'DateTimeField', 'verbose_name': '创建时间'},
                {'name': 'is_published', 'field_type': 'BooleanField', 'verbose_name': '已发布', 'default_value': 'False'},
            ]
            
            for i, field_data in enumerate(fields_data):
                field_data['order'] = i
                FieldDesign.objects.get_or_create(
                    model_design=article_model,
                    name=field_data['name'],
                    defaults=field_data
                )
                self.stdout.write(f'  创建字段: {field_data["name"]}')

        # 创建分类模型
        category_model, created = ModelDesign.objects.get_or_create(
            project=project,
            name='Category',
            defaults={
                'verbose_name': '分类',
                'verbose_name_plural': '分类',
                'description': '文章分类模型',
                'position_x': 400,
                'position_y': 100
            }
        )
        if created:
            self.stdout.write(f'创建模型: {category_model.name}')

            # 创建分类字段
            fields_data = [
                {'name': 'name', 'field_type': 'CharField', 'verbose_name': '名称', 'max_length': 100},
                {'name': 'description', 'field_type': 'TextField', 'verbose_name': '描述', 'blank': True},
                {'name': 'created_at', 'field_type': 'DateTimeField', 'verbose_name': '创建时间'},
            ]
            
            for i, field_data in enumerate(fields_data):
                field_data['order'] = i
                FieldDesign.objects.get_or_create(
                    model_design=category_model,
                    name=field_data['name'],
                    defaults=field_data
                )
                self.stdout.write(f'  创建字段: {field_data["name"]}')

        # 创建第二个测试项目
        project2, created = ModelProject.objects.get_or_create(
            name='电商系统',
            defaults={
                'app_name': 'ecommerce',
                'description': '电子商务系统示例',
                'owner': user,
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'创建测试项目: {project2.name}')

            # 创建产品模型
            product_model = ModelDesign.objects.create(
                project=project2,
                name='Product',
                verbose_name='产品',
                verbose_name_plural='产品',
                description='产品模型',
                position_x=150,
                position_y=150
            )
            self.stdout.write(f'创建模型: {product_model.name}')

            # 创建产品字段
            fields_data = [
                {'name': 'name', 'field_type': 'CharField', 'verbose_name': '产品名称', 'max_length': 200},
                {'name': 'price', 'field_type': 'DecimalField', 'verbose_name': '价格', 'max_digits': 10, 'decimal_places': 2},
                {'name': 'description', 'field_type': 'TextField', 'verbose_name': '描述'},
                {'name': 'stock', 'field_type': 'IntegerField', 'verbose_name': '库存'},
                {'name': 'is_active', 'field_type': 'BooleanField', 'verbose_name': '是否激活', 'default_value': 'True'},
            ]
            
            for i, field_data in enumerate(fields_data):
                field_data['order'] = i
                FieldDesign.objects.create(
                    model_design=product_model,
                    **field_data
                )
                self.stdout.write(f'  创建字段: {field_data["name"]}')

        self.stdout.write(self.style.SUCCESS('测试数据创建完成！'))
        self.stdout.write(f'项目总数: {ModelProject.objects.count()}')
        self.stdout.write(f'模型总数: {ModelDesign.objects.count()}')
        self.stdout.write(f'字段总数: {FieldDesign.objects.count()}')
