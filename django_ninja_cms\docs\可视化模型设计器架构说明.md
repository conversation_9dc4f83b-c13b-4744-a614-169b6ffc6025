# 可视化模型设计器 - 架构说明

## 📁 项目结构

```
apps/model_designer/
├── __init__.py
├── apps.py                 # 应用配置
├── models.py              # 数据模型
├── admin.py               # Django Admin 配置
├── api.py                 # API 端点
├── schemas.py             # Pydantic 数据模式
├── services.py            # 业务逻辑服务
├── views.py               # 视图函数
├── urls.py                # URL 配置
├── management/            # 管理命令
│   └── commands/
│       └── create_initial_templates.py
└── templates/             # 模板文件
    └── model_designer/
        ├── designer.html  # 设计器界面
        └── dashboard.html # 仪表板界面
```

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────────────────────────┐
│           前端界面层                  │
│  Vue.js 3 + Element Plus + Axios   │
├─────────────────────────────────────┤
│            API 层                   │
│      Django Ninja + Pydantic       │
├─────────────────────────────────────┤
│           服务层                     │
│    业务逻辑 + 代码生成 + YAML处理     │
├─────────────────────────────────────┤
│           数据层                     │
│      Django ORM + 数据模型          │
├─────────────────────────────────────┤
│          数据库层                    │
│    PostgreSQL / SQLite              │
└─────────────────────────────────────┘
```

### 核心组件

#### 1. 数据模型层 (`models.py`)

**ModelProject** - 项目管理
- 项目基本信息（名称、描述、应用名）
- 项目配置和版本管理
- 用户权限控制

**ModelDesign** - 模型设计
- 模型基本信息和元数据
- 位置信息（用于可视化布局）
- 模型选项（排序、索引、约束等）

**FieldDesign** - 字段设计
- 字段类型和属性配置
- 关系字段配置
- 验证器和帮助信息

**CodeGenerationHistory** - 生成历史
- 生成任务状态跟踪
- 生成选项和结果记录
- 错误信息和执行时间

**ModelTemplate** - 模型模板
- 预定义模型模板
- 模板分类和使用统计
- 模板配置 JSON 存储

**FieldPreset** - 字段预设
- 常用字段组合
- 标签分类系统
- 使用统计和管理

#### 2. 服务层 (`services.py`)

**ModelProjectService** - 项目管理服务
```python
# 主要方法
create_project()           # 创建项目
import_from_yaml()         # YAML 导入
export_project_to_yaml()   # YAML 导出
get_existing_models()      # 获取现有模型
```

**CodeGenerationService** - 代码生成服务
```python
# 主要方法
generate_code()            # 执行代码生成
_parse_generated_files()   # 解析生成结果
```

#### 3. API 层 (`api.py`)

**RESTful API 设计**
- 项目管理：`/api/model-designer/projects/`
- 模型设计：`/api/model-designer/models/`
- 字段设计：`/api/model-designer/fields/`
- 代码生成：`/api/model-designer/generate/`
- 配置管理：`/api/model-designer/import-yaml/`

**认证和权限**
- JWT Token 认证
- 用户级别权限控制
- 项目所有者权限验证

#### 4. 前端界面层

**技术栈**
- Vue.js 3 (Composition API)
- Element Plus (UI 组件库)
- Axios (HTTP 客户端)

**主要组件**
- 项目列表和管理
- 可视化模型设计器
- 字段编辑器
- 模板和预设选择器
- 代码生成控制台

## 🔄 数据流程

### 1. 项目创建流程

```mermaid
graph TD
    A[用户填写项目信息] --> B[前端验证]
    B --> C[API 调用]
    C --> D[服务层处理]
    D --> E[数据库保存]
    E --> F[返回项目信息]
    F --> G[界面更新]
```

### 2. 模型设计流程

```mermaid
graph TD
    A[添加模型] --> B[配置字段]
    B --> C[设置关系]
    C --> D[调整布局]
    D --> E[实时保存]
    E --> F[可视化更新]
```

### 3. 代码生成流程

```mermaid
graph TD
    A[选择生成选项] --> B[导出 YAML 配置]
    B --> C[调用生成命令]
    C --> D[执行代码生成]
    D --> E[解析生成结果]
    E --> F[更新历史记录]
    F --> G[返回生成状态]
```

## 🔧 技术实现细节

### 1. 可视化拖拽

**实现方式**
- 使用原生 JavaScript 事件处理
- 监听 mousedown、mousemove、mouseup 事件
- 实时更新模型位置坐标
- 自动保存位置到服务器

**关键代码**
```javascript
// 拖拽开始
startDrag(model, event) {
    this.dragModel = model;
    this.dragOffset = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
    };
}

// 拖拽移动
document.addEventListener('mousemove', (event) => {
    if (this.dragModel) {
        this.dragModel.position_x = event.clientX - canvasRect.left - this.dragOffset.x;
        this.dragModel.position_y = event.clientY - canvasRect.top - this.dragOffset.y;
    }
});
```

### 2. YAML 配置兼容

**配置格式**
```yaml
app_name: blog
models:
  Post:
    fields:
      title:
        type: str
        max_length: 200
        verbose_name: 标题
    options:
      verbose_name: 博客文章
      ordering: ['-created_at']
```

**解析逻辑**
- 支持简单字符串格式：`"str:200"`
- 支持完整字典格式：`{"type": "str", "max_length": 200}`
- 自动转换字段类型映射
- 处理关系字段的特殊配置

### 3. 代码生成集成

**集成方式**
- 复用现有的 `generate_from_config.py` 工具
- 通过 subprocess 调用生成命令
- 临时文件处理 YAML 配置
- 解析生成输出和错误信息

**生成选项**
- `--full`: 生成完整功能（模型、API、Admin等）
- `--with-frontend`: 生成前端 Vue 组件
- `--output-dir`: 指定输出目录

### 4. 数据验证

**Pydantic 模式**
- 严格的数据类型验证
- 自动文档生成
- 错误信息本地化
- 嵌套对象验证

**Django 模型验证**
- 数据库约束验证
- 自定义验证器
- 唯一性检查
- 外键关系验证

## 🚀 性能优化

### 1. 数据库优化

**索引策略**
- 主键和外键自动索引
- 查询频繁字段添加索引
- 复合索引优化查询

**查询优化**
- 使用 select_related 减少查询次数
- 分页加载大量数据
- 缓存常用查询结果

### 2. 前端优化

**组件优化**
- 虚拟滚动处理大量数据
- 防抖处理用户输入
- 组件懒加载

**网络优化**
- API 请求合并
- 响应数据压缩
- 静态资源 CDN

### 3. 缓存策略

**Redis 缓存**
- 用户会话缓存
- 模板和预设缓存
- 生成结果缓存

**浏览器缓存**
- 静态资源缓存
- API 响应缓存
- 本地存储优化

## 🔒 安全考虑

### 1. 认证授权

**JWT Token**
- 无状态认证
- Token 过期机制
- 刷新 Token 支持

**权限控制**
- 用户级别权限
- 项目所有者权限
- API 端点权限验证

### 2. 数据安全

**输入验证**
- SQL 注入防护
- XSS 攻击防护
- CSRF 保护

**代码生成安全**
- 路径遍历防护
- 文件权限控制
- 恶意代码检测

### 3. API 安全

**请求限制**
- 频率限制
- 请求大小限制
- 超时控制

**错误处理**
- 敏感信息隐藏
- 统一错误格式
- 日志记录

## 📈 扩展性设计

### 1. 插件系统

**字段类型扩展**
- 自定义字段类型注册
- 字段渲染器扩展
- 验证器插件

**模板系统扩展**
- 自定义模板格式
- 模板继承机制
- 动态模板加载

### 2. 多租户支持

**数据隔离**
- 租户级别数据分离
- 权限边界控制
- 资源配额管理

**配置隔离**
- 租户级别配置
- 自定义主题支持
- 功能开关控制

### 3. 国际化支持

**多语言**
- 界面文本国际化
- 错误信息本地化
- 时区处理

**本地化**
- 数字格式化
- 日期格式化
- 货币格式化

---

这个架构设计确保了系统的可维护性、可扩展性和高性能，为未来的功能扩展提供了坚实的基础。
