# Generated by Django 5.2.4 on 2025-07-13 14:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="FieldPreset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="preset name"
                    ),
                ),
                ("description", models.TextField(verbose_name="description")),
                (
                    "fields_config",
                    models.J<PERSON><PERSON>ield(default=list, verbose_name="fields config"),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="tags"),
                ),
                (
                    "usage_count",
                    models.IntegerField(default=0, verbose_name="usage count"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="is active"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_presets",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="created by",
                    ),
                ),
            ],
            options={
                "verbose_name": "Field Preset",
                "verbose_name_plural": "Field Presets",
                "db_table": "model_designer_field_presets",
                "ordering": ["-usage_count", "name"],
            },
        ),
        migrations.CreateModel(
            name="ModelProject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="project name"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="description"),
                ),
                ("app_name", models.CharField(max_length=50, verbose_name="app name")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="is active"),
                ),
                (
                    "version",
                    models.CharField(
                        default="1.0.0", max_length=20, verbose_name="version"
                    ),
                ),
                (
                    "config",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="project config"
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_projects",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="owner",
                    ),
                ),
            ],
            options={
                "verbose_name": "Model Project",
                "verbose_name_plural": "Model Projects",
                "db_table": "model_designer_projects",
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="ModelDesign",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="model name")),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="description"),
                ),
                (
                    "verbose_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="verbose name"
                    ),
                ),
                (
                    "verbose_name_plural",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="verbose name plural"
                    ),
                ),
                (
                    "db_table",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="database table"
                    ),
                ),
                (
                    "ordering",
                    models.JSONField(blank=True, default=list, verbose_name="ordering"),
                ),
                (
                    "indexes",
                    models.JSONField(blank=True, default=list, verbose_name="indexes"),
                ),
                (
                    "constraints",
                    models.JSONField(
                        blank=True, default=list, verbose_name="constraints"
                    ),
                ),
                (
                    "permissions",
                    models.JSONField(
                        blank=True, default=list, verbose_name="permissions"
                    ),
                ),
                (
                    "position_x",
                    models.IntegerField(default=0, verbose_name="position x"),
                ),
                (
                    "position_y",
                    models.IntegerField(default=0, verbose_name="position y"),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_designs",
                        to="model_designer.modelproject",
                        verbose_name="project",
                    ),
                ),
            ],
            options={
                "verbose_name": "Model Design",
                "verbose_name_plural": "Model Designs",
                "db_table": "model_designer_model_designs",
                "ordering": ["name"],
                "unique_together": {("project", "name")},
            },
        ),
        migrations.CreateModel(
            name="CodeGenerationHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("success", "Success"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="status",
                    ),
                ),
                (
                    "generate_full",
                    models.BooleanField(default=False, verbose_name="generate full"),
                ),
                (
                    "generate_frontend",
                    models.BooleanField(
                        default=False, verbose_name="generate frontend"
                    ),
                ),
                (
                    "output_directory",
                    models.CharField(
                        default="apps", max_length=200, verbose_name="output directory"
                    ),
                ),
                (
                    "generated_files",
                    models.JSONField(
                        blank=True, default=list, verbose_name="generated files"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, verbose_name="error message"),
                ),
                (
                    "execution_time",
                    models.FloatField(
                        blank=True, null=True, verbose_name="execution time"
                    ),
                ),
                (
                    "config_snapshot",
                    models.JSONField(
                        blank=True, default=dict, verbose_name="config snapshot"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="code_generations",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="user",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="generation_history",
                        to="model_designer.modelproject",
                        verbose_name="project",
                    ),
                ),
            ],
            options={
                "verbose_name": "Code Generation History",
                "verbose_name_plural": "Code Generation History",
                "db_table": "model_designer_generation_history",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ModelTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="template name"
                    ),
                ),
                ("description", models.TextField(verbose_name="description")),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("blog", "Blog"),
                            ("ecommerce", "E-commerce"),
                            ("cms", "CMS"),
                            ("user", "User Management"),
                            ("common", "Common"),
                            ("custom", "Custom"),
                        ],
                        max_length=20,
                        verbose_name="category",
                    ),
                ),
                (
                    "template_config",
                    models.JSONField(default=dict, verbose_name="template config"),
                ),
                (
                    "preview_image",
                    models.URLField(blank=True, verbose_name="preview image"),
                ),
                (
                    "usage_count",
                    models.IntegerField(default=0, verbose_name="usage count"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="is active"),
                ),
                (
                    "is_featured",
                    models.BooleanField(default=False, verbose_name="is featured"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_templates",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="created by",
                    ),
                ),
            ],
            options={
                "verbose_name": "Model Template",
                "verbose_name_plural": "Model Templates",
                "db_table": "model_designer_templates",
                "ordering": ["-is_featured", "-usage_count", "name"],
            },
        ),
        migrations.CreateModel(
            name="FieldDesign",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                ("name", models.CharField(max_length=100, verbose_name="field name")),
                (
                    "field_type",
                    models.CharField(
                        choices=[
                            ("str", "String"),
                            ("text", "Text"),
                            ("int", "Integer"),
                            ("float", "Float"),
                            ("decimal", "Decimal"),
                            ("bool", "Boolean"),
                            ("date", "Date"),
                            ("datetime", "DateTime"),
                            ("time", "Time"),
                            ("email", "Email"),
                            ("url", "URL"),
                            ("slug", "Slug"),
                            ("uuid", "UUID"),
                            ("json", "JSON"),
                            ("file", "File"),
                            ("image", "Image"),
                            ("choices", "Choices"),
                            ("fk", "Foreign Key"),
                            ("m2m", "Many to Many"),
                            ("o2o", "One to One"),
                        ],
                        max_length=20,
                        verbose_name="field type",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, verbose_name="description"),
                ),
                ("null", models.BooleanField(default=False, verbose_name="null")),
                ("blank", models.BooleanField(default=False, verbose_name="blank")),
                ("unique", models.BooleanField(default=False, verbose_name="unique")),
                (
                    "db_index",
                    models.BooleanField(default=False, verbose_name="db index"),
                ),
                (
                    "max_length",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="max length"
                    ),
                ),
                (
                    "max_digits",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="max digits"
                    ),
                ),
                (
                    "decimal_places",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="decimal places"
                    ),
                ),
                (
                    "default_value",
                    models.TextField(blank=True, verbose_name="default value"),
                ),
                (
                    "validators",
                    models.JSONField(
                        blank=True, default=list, verbose_name="validators"
                    ),
                ),
                ("help_text", models.TextField(blank=True, verbose_name="help text")),
                (
                    "verbose_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="verbose name"
                    ),
                ),
                (
                    "related_model",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="related model"
                    ),
                ),
                (
                    "on_delete",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("CASCADE", "CASCADE"),
                            ("PROTECT", "PROTECT"),
                            ("SET_NULL", "SET_NULL"),
                            ("SET_DEFAULT", "SET_DEFAULT"),
                            ("DO_NOTHING", "DO_NOTHING"),
                        ],
                        max_length=20,
                        verbose_name="on delete",
                    ),
                ),
                (
                    "related_name",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="related name"
                    ),
                ),
                (
                    "upload_to",
                    models.CharField(
                        blank=True, max_length=200, verbose_name="upload to"
                    ),
                ),
                (
                    "choices",
                    models.JSONField(blank=True, default=list, verbose_name="choices"),
                ),
                ("order", models.IntegerField(default=0, verbose_name="order")),
                (
                    "model_design",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="field_designs",
                        to="model_designer.modeldesign",
                        verbose_name="model design",
                    ),
                ),
            ],
            options={
                "verbose_name": "Field Design",
                "verbose_name_plural": "Field Designs",
                "db_table": "model_designer_field_designs",
                "ordering": ["order", "name"],
                "unique_together": {("model_design", "name")},
            },
        ),
    ]
