# 🚀 代码生成工具完整指南

Django Ninja CMS 提供了强大的代码生成工具，可以帮助您快速构建完整的应用模块。

## 📋 目录

- [基于配置文件的批量生成](#基于配置文件的批量生成)
- [单个模块生成](#单个模块生成)
- [配置文件格式](#配置文件格式)
- [字段类型支持](#字段类型支持)
- [生成的文件结构](#生成的文件结构)
- [最佳实践](#最佳实践)

## 🔧 基于配置文件的批量生成

### 命令格式

```bash
python manage.py generate_from_config --config <配置文件> [选项]
```

### 可用选项

| 选项 | 说明 | 默认值 |
|------|------|--------|
| `--config` | YAML 配置文件路径 | 必需 |
| `--full` | 生成完整功能（模型、API、Admin、权限、测试） | False |
| `--with-frontend` | 生成前端 Vue.js 组件 | False |
| `--output-dir` | 输出目录 | apps |
| `--dry-run` | 预览模式，不实际生成文件 | False |

### 使用示例

```bash
# 生成完整的 CMS 系统
python manage.py generate_from_config --config cms_config.yaml --full --with-frontend

# 预览生成内容
python manage.py generate_from_config --config cms_config.yaml --full --dry-run

# 只生成后端代码
python manage.py generate_from_config --config cms_config.yaml --full

# 自定义输出目录
python manage.py generate_from_config --config cms_config.yaml --output-dir my_apps
```

## 🔨 单个模块生成

### 生成完整模块

```bash
python manage.py generate module <模块名> [选项]
```

**选项：**
- `--fields` - 字段定义
- `--permissions` - 权限前缀
- `--admin` - 生成 Admin 配置
- `--api` - 生成 API 接口

**示例：**
```bash
# 生成博客模块
python manage.py generate module blog \
  --fields "title:str,content:text,is_published:bool,author:fk:User" \
  --admin --api

# 生成产品模块
python manage.py generate module product \
  --fields "name:str,price:float,description:text,category:fk:Category" \
  --permissions "shop.product" \
  --admin --api
```

### 生成单个模型

```bash
python manage.py generate model <应用名> <模型名> --fields <字段定义>
```

**示例：**
```bash
# 在 blog 应用中生成 Comment 模型
python manage.py generate model blog Comment \
  --fields "content:text,author:fk:User,post:fk:Post,is_approved:bool"
```

### 生成 API 接口

```bash
python manage.py generate api <应用名> <模型名> [--permissions <权限前缀>]
```

**示例：**
```bash
# 为 blog 应用的 Post 模型生成 API
python manage.py generate api blog Post --permissions "blog.post"
```

## 📄 配置文件格式

### 基本结构

```yaml
app_name: <应用名称>
models:
  <模型名>:
    fields:
      <字段名>: <字段类型>
    options:
      ordering: ['-created_at']
      verbose_name: '<显示名称>'
```

### 简化配置示例

```yaml
app_name: cms
models:
  Article:
    fields:
      title: str
      slug: str
      content: text
      summary: str
      status: choices:draft,published,archived
      is_featured: bool
      view_count: int
      author: fk:User
      category: fk:Category
      tags: m2m:Tag
    options:
      ordering: ['-created_at']
      verbose_name: '文章'

  Category:
    fields:
      name: str
      slug: str
      description: text
      parent: fk:self
      is_active: bool
      sort_order: int
    options:
      ordering: ['sort_order', 'name']
      verbose_name: '分类'
```

### 增强配置示例

```yaml
app_name: enhanced_blog
models:
  Article:
    fields:
      # 基本字符串字段
      title:
        type: str
        max_length: 200
        null: false
        blank: false
        help_text: "文章标题，最多200个字符"
        verbose_name: "标题"

      # 唯一字段
      slug:
        type: str
        max_length: 100
        unique: true
        help_text: "URL友好的标识符"
        verbose_name: "URL标识"

      # 长文本字段
      content:
        type: text
        null: false
        blank: false
        help_text: "文章正文内容"
        verbose_name: "内容"

      # 选择字段
      status:
        type: choices
        choices:
          - ["draft", "草稿"]
          - ["published", "已发布"]
          - ["archived", "已归档"]
        default: "draft"
        help_text: "文章发布状态"
        verbose_name: "状态"

      # 外键字段
      author:
        type: fk
        to: User
        on_delete: CASCADE
        null: false
        help_text: "文章作者"
        verbose_name: "作者"
        related_name: "authored_articles"

      # 多对多字段
      tags:
        type: m2m
        to: Tag
        blank: true
        help_text: "文章标签"
        verbose_name: "标签"
        related_name: "tagged_articles"

    options:
      ordering: ['-created_at']
      verbose_name: '文章'
      verbose_name_plural: '文章'
      db_table: 'enhanced_blog_article'
      indexes:
        - fields: ['status', 'created_at']
        - fields: ['author', 'status']
      constraints:
        - unique_together: [['slug', 'author']]
```

## 🎯 字段类型支持

### 基本字段类型

| 类型 | Django 字段 | Pydantic 类型 | 说明 |
|------|-------------|---------------|------|
| `str` | `CharField(max_length=255)` | `str` | 字符串 |
| `text` | `TextField()` | `str` | 长文本 |
| `int` | `IntegerField()` | `int` | 整数 |
| `float` | `FloatField()` | `float` | 浮点数 |
| `bool` | `BooleanField(default=False)` | `bool` | 布尔值 |
| `date` | `DateField()` | `date` | 日期 |
| `datetime` | `DateTimeField()` | `datetime` | 日期时间 |
| `time` | `TimeField()` | `time` | 时间 |
| `email` | `EmailField()` | `str` | 邮箱 |
| `url` | `URLField()` | `str` | URL |
| `json` | `JSONField(default=dict)` | `dict` | JSON 数据 |
| `file` | `FileField(upload_to="uploads/")` | `str` | 文件 |
| `image` | `ImageField(upload_to="images/")` | `str` | 图片 |
| `choices` | `CharField(choices=...)` | `str` | 选择字段 |

### 数字字段类型

| 类型 | Django 字段 | Pydantic 类型 | 说明 |
|------|-------------|---------------|------|
| `bigint` | `BigIntegerField()` | `int` | 大整数 |
| `smallint` | `SmallIntegerField()` | `int` | 小整数 |
| `positiveint` | `PositiveIntegerField()` | `int` | 正整数 |
| `positivesmallint` | `PositiveSmallIntegerField()` | `int` | 正小整数 |
| `decimal` | `DecimalField(max_digits=10, decimal_places=2)` | `Decimal` | 精确小数 |

### 特殊字段类型

| 类型 | Django 字段 | Pydantic 类型 | 说明 |
|------|-------------|---------------|------|
| `slug` | `SlugField(max_length=50)` | `str` | URL 友好字符串 |
| `uuid` | `UUIDField()` | `UUID` | UUID 标识符 |
| `binary` | `BinaryField()` | `bytes` | 二进制数据 |
| `DurationField` | `DurationField()` | `timedelta` | 时间间隔 |
| `FilePathField` | `FilePathField(path="/")` | `str` | 文件路径 |
| `GenericIPAddressField` | `GenericIPAddressField()` | `str` | IP 地址 |

### 原生 Django 字段类型支持

支持直接使用 Django 原生字段类型名称：

| 原生类型 | 说明 | 示例 |
|----------|------|------|
| `CharField` | 字符字段 | `type: CharField` |
| `TextField` | 文本字段 | `type: TextField` |
| `IntegerField` | 整数字段 | `type: IntegerField` |
| `BigIntegerField` | 大整数字段 | `type: BigIntegerField` |
| `SmallIntegerField` | 小整数字段 | `type: SmallIntegerField` |
| `PositiveIntegerField` | 正整数字段 | `type: PositiveIntegerField` |
| `PositiveSmallIntegerField` | 正小整数字段 | `type: PositiveSmallIntegerField` |
| `FloatField` | 浮点数字段 | `type: FloatField` |
| `DecimalField` | 精确小数字段 | `type: DecimalField` |
| `BooleanField` | 布尔字段 | `type: BooleanField` |
| `DateField` | 日期字段 | `type: DateField` |
| `DateTimeField` | 日期时间字段 | `type: DateTimeField` |
| `TimeField` | 时间字段 | `type: TimeField` |
| `DurationField` | 时间间隔字段 | `type: DurationField` |
| `EmailField` | 邮箱字段 | `type: EmailField` |
| `URLField` | URL 字段 | `type: URLField` |
| `SlugField` | Slug 字段 | `type: SlugField` |
| `UUIDField` | UUID 字段 | `type: UUIDField` |
| `JSONField` | JSON 字段 | `type: JSONField` |
| `BinaryField` | 二进制字段 | `type: BinaryField` |
| `FileField` | 文件字段 | `type: FileField` |
| `ImageField` | 图片字段 | `type: ImageField` |
| `FilePathField` | 文件路径字段 | `type: FilePathField` |
| `GenericIPAddressField` | IP 地址字段 | `type: GenericIPAddressField` |

### 关系字段类型

| 类型 | Django 字段 | 说明 | 示例 |
|------|-------------|------|------|
| `fk` | `ForeignKey("Model", on_delete=models.CASCADE)` | 外键关系 | `author: fk:User` |
| `m2m` | `ManyToManyField("Model", blank=True)` | 多对多关系 | `tags: m2m:Tag` |

### 简化字段定义（兼容旧格式）

```yaml
# 基本字段
fields:
  name: str
  age: int
  is_active: bool
  author: fk:User
  tags: m2m:Tag
  status: choices:draft,published,archived
```

### 增强字段定义（推荐格式）

支持更多字段参数和配置选项：

```yaml
fields:
  # 字符串字段
  title:
    type: str
    max_length: 200
    null: false
    blank: false
    unique: false
    help_text: "文章标题"
    verbose_name: "标题"

  # 选择字段
  status:
    type: choices
    choices:
      - ["draft", "草稿"]
      - ["published", "已发布"]
      - ["archived", "已归档"]
    default: "draft"
    help_text: "发布状态"
    verbose_name: "状态"

  # 外键字段
  author:
    type: fk
    to: User
    on_delete: CASCADE
    null: false
    help_text: "文章作者"
    verbose_name: "作者"
    related_name: "authored_articles"

  # 多对多字段
  tags:
    type: m2m
    to: Tag
    blank: true
    help_text: "文章标签"
    verbose_name: "标签"
    related_name: "tagged_articles"

  # 文件字段
  attachment:
    type: file
    upload_to: "articles/attachments/"
    null: true
    blank: true
    help_text: "附件文件"
    verbose_name: "附件"

  # 图片字段
  featured_image:
    type: image
    upload_to: "articles/images/"
    null: true
    blank: true
    help_text: "特色图片"
    verbose_name: "特色图片"

  # UUID 字段
  uuid_id:
    type: uuid
    primary_key: true
    help_text: "UUID 主键"
    verbose_name: "UUID"

  # 精确小数字段
  price:
    type: decimal
    max_digits: 10
    decimal_places: 2
    null: true
    blank: true
    help_text: "价格"
    verbose_name: "价格"

  # Slug 字段
  article_slug:
    type: slug
    max_length: 100
    unique: true
    help_text: "URL 标识符"
    verbose_name: "标识符"

  # IP 地址字段
  client_ip:
    type: GenericIPAddressField
    null: true
    blank: true
    help_text: "客户端 IP 地址"
    verbose_name: "IP 地址"
```

### 支持的字段参数

| 参数 | 类型 | 说明 | 适用字段 |
|------|------|------|----------|
| `type` | string | 字段类型 | 所有字段 |
| `max_length` | int | 最大长度 | str, choices, email, url, slug |
| `null` | bool | 允许为空 | 所有字段 |
| `blank` | bool | 允许空白 | 所有字段 |
| `unique` | bool | 唯一约束 | 所有字段 |
| `default` | any | 默认值 | 所有字段 |
| `help_text` | string | 帮助文本 | 所有字段 |
| `verbose_name` | string | 显示名称 | 所有字段 |
| `choices` | list | 选择项 | choices |
| `to` | string | 关联模型 | fk, m2m |
| `on_delete` | string | 删除行为 | fk |
| `related_name` | string | 反向关系名 | fk, m2m |
| `upload_to` | string | 上传路径 | file, image |
| `validators` | list | 验证器 | 所有字段 |
| `max_digits` | int | 最大位数 | decimal |
| `decimal_places` | int | 小数位数 | decimal |
| `primary_key` | bool | 主键 | uuid, 所有字段 |
| `editable` | bool | 可编辑 | 所有字段 |
| `auto_now` | bool | 自动更新时间 | date, datetime, time |
| `auto_now_add` | bool | 自动添加时间 | date, datetime, time |
| `height_field` | string | 高度字段名 | image |
| `width_field` | string | 宽度字段名 | image |
| `path` | string | 文件路径 | FilePathField |
| `match` | string | 文件匹配模式 | FilePathField |
| `recursive` | bool | 递归搜索 | FilePathField |
| `protocol` | string | IP 协议 (both/IPv4/IPv6) | GenericIPAddressField |
| `unpack_ipv4` | bool | 解包 IPv4 | GenericIPAddressField |
| `populate_from` | string | 自动填充来源字段 | slug |

## 📁 生成的文件结构

### 后端文件

```
apps/<app_name>/
├── __init__.py          # 应用初始化
├── apps.py              # 应用配置
├── models.py            # 数据模型
├── admin.py             # Admin 配置
├── api.py               # API 接口
├── schemas.py           # Pydantic 模式
├── services.py          # 业务逻辑
├── permissions.py       # 权限配置
├── tests.py             # 测试用例
└── migrations/
    └── __init__.py      # 迁移文件
```

### 前端文件（使用 --with-frontend）

```
frontend/src/components/<app_name>/
├── <Model1>.vue         # Vue 组件
├── <Model2>.vue         # Vue 组件
├── ...
└── router.js            # 路由配置
```

## 🎯 最佳实践

### 1. 配置文件组织

```yaml
# 推荐：按功能模块组织
app_name: ecommerce
models:
  # 产品相关
  Product:
    fields: ...
  Category:
    fields: ...
  
  # 订单相关
  Order:
    fields: ...
  OrderItem:
    fields: ...
```

### 2. 字段命名规范

```yaml
# 推荐的字段命名
fields:
  title: str           # 简洁明了
  is_published: bool   # 布尔字段使用 is_ 前缀
  created_at: datetime # 时间字段使用 _at 后缀
  user_id: fk:User     # 外键字段使用 _id 后缀（可选）
```

### 3. 权限设计

```bash
# 推荐的权限前缀格式
--permissions "app.model"

# 示例
--permissions "blog.post"      # blog 应用的 post 模型
--permissions "shop.product"   # shop 应用的 product 模型
```

### 4. 生成流程

```bash
# 1. 预览生成内容
python manage.py generate_from_config --config config.yaml --dry-run

# 2. 实际生成文件
python manage.py generate_from_config --config config.yaml --full --with-frontend

# 3. 添加到 INSTALLED_APPS
# 编辑 core/settings/base.py

# 4. 生成迁移
python manage.py makemigrations <app_name>

# 5. 执行迁移
python manage.py migrate

# 6. 生成权限
python manage.py auto_generate_permissions <app_name> --create-roles

# 7. 添加路由
# 编辑 core/urls.py
```

### 5. 自定义扩展

生成的代码是基础模板，您可以根据需要进行自定义：

- **模型扩展**：添加自定义方法、属性、Meta 选项
- **API 扩展**：添加自定义端点、验证逻辑
- **前端扩展**：自定义样式、添加功能组件

## 🔍 故障排除

### 常见问题

1. **配置文件解析错误**
   ```bash
   # 检查 YAML 语法
   python -c "import yaml; yaml.safe_load(open('config.yaml'))"
   ```

2. **字段类型不支持**
   ```bash
   # 查看支持的字段类型
   python manage.py generate_from_config --help
   ```

3. **权限生成失败**
   ```bash
   # 手动生成权限
   python manage.py auto_generate_permissions <app_name> --create-roles
   ```

### 调试技巧

- 使用 `--dry-run` 预览生成内容
- 使用 `--verbosity 2` 查看详细输出
- 检查生成的文件语法是否正确

---

**提示**：代码生成工具旨在提供基础结构，生成后请根据具体需求进行调整和优化。
