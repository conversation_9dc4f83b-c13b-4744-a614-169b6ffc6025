"""
Model Designer services.
"""
import os
import yaml
import json
import time
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.apps import apps
from django.db import transaction


class ModelProjectService:
    """
    模型项目管理服务
    """
    
    @classmethod
    def create_project(cls, name: str, app_name: str, owner, description: str = "") -> 'ModelProject':
        """创建新的模型项目"""
        from .models import ModelProject
        
        project = ModelProject.objects.create(
            name=name,
            app_name=app_name,
            owner=owner,
            description=description
        )
        return project
    
    @classmethod
    def import_from_yaml(cls, yaml_content: str, owner) -> 'ModelProject':
        """从 YAML 配置导入项目"""
        from .models import ModelProject, ModelDesign, FieldDesign
        
        try:
            config = yaml.safe_load(yaml_content)
        except yaml.YAMLError as e:
            raise ValueError(f"YAML 解析错误: {e}")
        
        if 'app_name' not in config:
            raise ValueError("配置文件必须包含 app_name 字段")
        
        if 'models' not in config:
            raise ValueError("配置文件必须包含 models 字段")
        
        with transaction.atomic():
            # 创建项目
            project = ModelProject.objects.create(
                name=f"{config['app_name']}_imported",
                app_name=config['app_name'],
                owner=owner,
                description=f"从 YAML 配置导入的项目",
                config=config
            )
            
            # 创建模型设计
            for model_name, model_config in config['models'].items():
                model_design = ModelDesign.objects.create(
                    project=project,
                    name=model_name,
                    description=model_config.get('description', ''),
                    verbose_name=model_config.get('options', {}).get('verbose_name', ''),
                    verbose_name_plural=model_config.get('options', {}).get('verbose_name_plural', ''),
                    db_table=model_config.get('options', {}).get('db_table', ''),
                    ordering=model_config.get('options', {}).get('ordering', []),
                    indexes=model_config.get('options', {}).get('indexes', []),
                    constraints=model_config.get('options', {}).get('constraints', []),
                    permissions=model_config.get('options', {}).get('permissions', [])
                )
                
                # 创建字段设计
                fields = model_config.get('fields', {})
                for order, (field_name, field_config) in enumerate(fields.items()):
                    cls._create_field_design(model_design, field_name, field_config, order)
        
        return project
    
    @classmethod
    def _create_field_design(cls, model_design, field_name: str, field_config, order: int):
        """创建字段设计"""
        from .models import FieldDesign
        
        # 处理简单字符串格式的字段配置
        if isinstance(field_config, str):
            field_config = cls._parse_simple_field_config(field_config)
        
        field_design = FieldDesign.objects.create(
            model_design=model_design,
            name=field_name,
            field_type=field_config.get('type', 'str'),
            description=field_config.get('description', ''),
            null=field_config.get('null', False),
            blank=field_config.get('blank', False),
            unique=field_config.get('unique', False),
            db_index=field_config.get('db_index', False),
            max_length=field_config.get('max_length'),
            max_digits=field_config.get('max_digits'),
            decimal_places=field_config.get('decimal_places'),
            default_value=str(field_config.get('default', '')) if field_config.get('default') is not None else '',
            validators=field_config.get('validators', []),
            help_text=field_config.get('help_text', ''),
            verbose_name=field_config.get('verbose_name', ''),
            related_model=field_config.get('to', ''),
            on_delete=field_config.get('on_delete', ''),
            related_name=field_config.get('related_name', ''),
            upload_to=field_config.get('upload_to', ''),
            choices=field_config.get('choices', []),
            order=order
        )
        
        return field_design
    
    @classmethod
    def _parse_simple_field_config(cls, field_config_str: str) -> Dict[str, Any]:
        """解析简单字符串格式的字段配置"""
        config = {}
        
        if ':' in field_config_str:
            field_type, params = field_config_str.split(':', 1)
            config['type'] = field_type
            
            if field_type == 'choices':
                config['choices'] = [[choice.strip(), choice.strip()] for choice in params.split(',')]
            elif field_type in ['fk', 'm2m', 'o2o']:
                config['to'] = params
                if field_type == 'fk':
                    config['on_delete'] = 'CASCADE'
        else:
            config['type'] = field_config_str
        
        return config
    
    @classmethod
    def export_project_to_yaml(cls, project: 'ModelProject') -> str:
        """导出项目为 YAML 配置"""
        config = {
            'app_name': project.app_name,
            'models': {}
        }
        
        for model_design in project.model_designs.all():
            model_config = {
                'fields': {},
                'options': {}
            }
            
            # 添加字段
            for field_design in model_design.field_designs.all():
                field_config = field_design.get_field_definition()
                model_config['fields'][field_design.name] = field_config
            
            # 添加模型选项
            if model_design.verbose_name:
                model_config['options']['verbose_name'] = model_design.verbose_name
            if model_design.verbose_name_plural:
                model_config['options']['verbose_name_plural'] = model_design.verbose_name_plural
            if model_design.db_table:
                model_config['options']['db_table'] = model_design.db_table
            if model_design.ordering:
                model_config['options']['ordering'] = model_design.ordering
            if model_design.indexes:
                model_config['options']['indexes'] = model_design.indexes
            if model_design.constraints:
                model_config['options']['constraints'] = model_design.constraints
            if model_design.permissions:
                model_config['options']['permissions'] = model_design.permissions
            
            config['models'][model_design.name] = model_config
        
        return yaml.dump(config, default_flow_style=False, allow_unicode=True)
    
    @classmethod
    def get_existing_models(cls) -> List[Dict[str, Any]]:
        """获取现有的 Django 模型信息"""
        models_info = []
        
        for app_config in apps.get_app_configs():
            if app_config.name.startswith('apps.'):
                for model in app_config.get_models():
                    model_info = {
                        'app_name': app_config.label,
                        'model_name': model.__name__,
                        'verbose_name': str(model._meta.verbose_name),
                        'fields': []
                    }
                    
                    for field in model._meta.get_fields():
                        field_info = {
                            'name': field.name,
                            'type': field.__class__.__name__,
                            'verbose_name': getattr(field, 'verbose_name', ''),
                            'help_text': getattr(field, 'help_text', ''),
                            'null': getattr(field, 'null', False),
                            'blank': getattr(field, 'blank', False),
                        }
                        
                        if hasattr(field, 'related_model') and field.related_model:
                            field_info['related_model'] = field.related_model.__name__
                        
                        model_info['fields'].append(field_info)
                    
                    models_info.append(model_info)
        
        return models_info


class CodeGenerationService:
    """
    代码生成服务
    """
    
    @classmethod
    def generate_code(cls, project: 'ModelProject', user, options: Dict[str, Any]) -> 'CodeGenerationHistory':
        """生成代码"""
        from .models import CodeGenerationHistory
        
        # 创建生成历史记录
        history = CodeGenerationHistory.objects.create(
            project=project,
            user=user,
            status='running',
            generate_full=options.get('full', False),
            generate_frontend=options.get('with_frontend', False),
            output_directory=options.get('output_dir', 'apps'),
            config_snapshot=project.config
        )
        
        try:
            start_time = time.time()
            
            # 导出项目配置为临时 YAML 文件
            yaml_content = ModelProjectService.export_project_to_yaml(project)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, encoding='utf-8') as f:
                f.write(yaml_content)
                temp_yaml_path = f.name
            
            try:
                # 构建命令参数
                cmd_args = [
                    'python', 'manage.py', 'generate_from_config',
                    '--config', temp_yaml_path,
                    '--output-dir', options.get('output_dir', 'apps')
                ]
                
                if options.get('full', False):
                    cmd_args.append('--full')
                
                if options.get('with_frontend', False):
                    cmd_args.append('--with-frontend')
                
                # 执行代码生成命令
                result = subprocess.run(
                    cmd_args,
                    capture_output=True,
                    text=True,
                    cwd=settings.BASE_DIR
                )
                
                execution_time = time.time() - start_time
                
                if result.returncode == 0:
                    # 生成成功
                    history.status = 'success'
                    history.execution_time = execution_time
                    history.generated_files = cls._parse_generated_files(result.stdout)
                else:
                    # 生成失败
                    history.status = 'failed'
                    history.error_message = result.stderr or result.stdout
                    history.execution_time = execution_time
                
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_yaml_path)
                except OSError:
                    pass
        
        except Exception as e:
            history.status = 'failed'
            history.error_message = str(e)
            history.execution_time = time.time() - start_time if 'start_time' in locals() else 0
        
        history.save()
        return history
    
    @classmethod
    def _parse_generated_files(cls, output: str) -> List[str]:
        """解析生成的文件列表"""
        files = []
        lines = output.split('\n')
        
        for line in lines:
            line = line.strip()
            if '✓' in line and ('.py' in line or '.vue' in line):
                # 提取文件名
                parts = line.split('✓')
                if len(parts) > 1:
                    filename = parts[1].strip()
                    files.append(filename)
        
        return files
