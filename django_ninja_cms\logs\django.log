INFO 2025-07-12 19:12:48,935 autoreload 30324 24664 Watching for file changes with StatReloader
INFO 2025-07-12 19:13:13,446 logging 30324 6560 Request started - ID: ae91dff2-861b-4f1f-b0b9-84d170e16af4 | Method: GET | Path: / | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:13:13,499 logging 30324 6560 Request completed - ID: ae91dff2-861b-4f1f-b0b9-84d170e16af4 | Status: 404 | Duration: 0.055s
WARNING 2025-07-12 19:13:13,500 log 30324 6560 Not Found: /
WARNING 2025-07-12 19:13:13,501 basehttp 30324 6560 "GET / HTTP/1.1" 404 16057
INFO 2025-07-12 19:13:25,013 logging 30324 6560 Request started - ID: 374fb343-fe93-4d9f-8008-cc7f65a9eb79 | Method: GET | Path: /api/ | User:  | IP: 127.0.0.1
ERROR 2025-07-12 19:13:25,015 logging 30324 6560 Request failed - ID: 374fb343-fe93-4d9f-8008-cc7f65a9eb79 | Exception: Http404: docs_url = /api/api/docs/
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\openapi\views.py", line 16, in default_home
    raise Http404(f"docs_url = {docs_url}")
django.http.response.Http404: docs_url = /api/api/docs/
INFO 2025-07-12 19:13:25,090 logging 30324 6560 Request completed - ID: 374fb343-fe93-4d9f-8008-cc7f65a9eb79 | Status: 404 | Duration: 0.077s
WARNING 2025-07-12 19:13:25,091 log 30324 6560 Not Found: /api/
WARNING 2025-07-12 19:13:25,091 basehttp 30324 6560 "GET /api/ HTTP/1.1" 404 29190
INFO 2025-07-12 19:13:35,307 logging 30324 2512 Request started - ID: 03049b44-6717-4231-92db-83c10fe1a4ff | Method: GET | Path: /admin/ | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:13:35,361 logging 30324 2512 Request completed - ID: 03049b44-6717-4231-92db-83c10fe1a4ff | Status: 302 | Duration: 0.054s
INFO 2025-07-12 19:13:35,362 basehttp 30324 2512 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-12 19:13:35,378 logging 30324 2512 Request started - ID: 45e971cf-8a1b-44c5-8824-276bffa1cee5 | Method: GET | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:13:35,438 logging 30324 2512 Request completed - ID: 45e971cf-8a1b-44c5-8824-276bffa1cee5 | Status: 200 | Duration: 0.060s
INFO 2025-07-12 19:13:35,439 basehttp 30324 2512 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 17343
INFO 2025-07-12 19:13:38,969 logging 30324 2512 Request started - ID: 29004efd-4d24-43e5-9a8b-f1674b932a73 | Method: POST | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:13:40,395 logging 30324 2512 Request completed - ID: 29004efd-4d24-43e5-9a8b-f1674b932a73 | Status: 200 | Duration: 1.427s
INFO 2025-07-12 19:13:40,396 basehttp 30324 2512 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 17514
INFO 2025-07-12 19:13:56,296 logging 30324 6560 Request started - ID: 276e2205-8531-4c9c-bfe8-b12542064dfb | Method: GET | Path: /api/core/ping | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:13:56,315 logging 30324 6560 Request completed - ID: 276e2205-8531-4c9c-bfe8-b12542064dfb | Status: 200 | Duration: 0.019s
INFO 2025-07-12 19:13:56,315 basehttp 30324 6560 "GET /api/core/ping HTTP/1.1" 200 62
INFO 2025-07-12 19:14:04,923 logging 30324 2512 Request started - ID: b790035c-38d4-4ee2-a22e-be42e158206f | Method: GET | Path: /admin/ | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:14:04,956 logging 30324 2512 Request completed - ID: b790035c-38d4-4ee2-a22e-be42e158206f | Status: 302 | Duration: 0.033s
INFO 2025-07-12 19:14:04,957 basehttp 30324 2512 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-12 19:14:04,969 logging 30324 2512 Request started - ID: 0e1dc9ac-ae0c-428b-99cf-e20a6a4cfb41 | Method: GET | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-12 19:14:05,001 logging 30324 2512 Request completed - ID: 0e1dc9ac-ae0c-428b-99cf-e20a6a4cfb41 | Status: 200 | Duration: 0.032s
INFO 2025-07-12 19:14:05,002 basehttp 30324 2512 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 17343
INFO 2025-07-12 19:15:04,379 autoreload 30324 24664 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-12 19:15:06,551 autoreload 28004 8420 Watching for file changes with StatReloader
INFO 2025-07-12 19:16:30,975 autoreload 28004 8420 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-12 19:16:33,209 autoreload 14056 14356 Watching for file changes with StatReloader
INFO 2025-07-12 19:17:08,227 autoreload 14056 14356 F:\PycharmProjects\django_ninja_cms\apps\core\admin\auto_admin.py changed, reloading.
INFO 2025-07-12 19:17:10,397 autoreload 30520 15984 Watching for file changes with StatReloader
INFO 2025-07-12 19:25:18,853 autoreload 30520 15984 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-12 19:25:21,216 autoreload 26040 17292 Watching for file changes with StatReloader
INFO 2025-07-12 19:25:55,588 autoreload 26040 17292 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-12 19:25:57,924 autoreload 16616 32664 Watching for file changes with StatReloader
INFO 2025-07-12 19:27:15,385 autoreload 16616 32664 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-12 19:27:17,363 autoreload 30848 6304 Watching for file changes with StatReloader
INFO 2025-07-12 19:28:15,172 autoreload 30848 6304 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-12 19:28:17,360 autoreload 2856 20460 Watching for file changes with StatReloader
INFO 2025-07-12 21:54:34,858 autoreload 28088 32940 Watching for file changes with StatReloader
INFO 2025-07-12 21:55:49,630 logging 28088 26356 Request started - ID: af31f75a-5dcd-4075-a6dd-950a6af63f60 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-12 21:55:51,476 logging 28088 26356 Request completed - ID: af31f75a-5dcd-4075-a6dd-950a6af63f60 | Status: 200 | Duration: 1.846s
INFO 2025-07-12 21:55:51,477 basehttp 28088 26356 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-12 21:56:03,557 logging 28088 26356 Request started - ID: c2590939-2131-4ed0-8889-1f67003c311f | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
ERROR 2025-07-12 21:56:03,566 errors 28088 26356 no such table: auth_login_attempts
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: auth_login_attempts

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\authentication\api.py", line 31, in login
    user = AuthService.authenticate_user(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\authentication\services.py", line 29, in authenticate_user
    if cls._is_rate_limited(email, ip_address):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\authentication\services.py", line 156, in _is_rate_limited
    ).count()
      ^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 604, in count
    return self.query.get_count(using=self.db)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\query.py", line 644, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\query.py", line 626, in get_aggregation
    result = compiler.execute_sql(SINGLE)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
           ^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: auth_login_attempts
INFO 2025-07-12 21:56:03,607 logging 28088 26356 Request completed - ID: c2590939-2131-4ed0-8889-1f67003c311f | Status: 500 | Duration: 0.050s
ERROR 2025-07-12 21:56:03,609 log 28088 26356 Internal Server Error: /api/auth/login
ERROR 2025-07-12 21:56:03,610 basehttp 28088 26356 "POST /api/auth/login HTTP/1.1" 500 4071
INFO 2025-07-12 21:57:12,172 autoreload 13376 33680 Watching for file changes with StatReloader
INFO 2025-07-12 21:57:36,794 logging 13376 23564 Request started - ID: 2c3e3f36-7563-4c9e-8c1c-12d1bbc3c5f2 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
ERROR 2025-07-12 21:57:38,240 errors 13376 23564 NOT NULL constraint failed: auth_login_attempts.failure_reason
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: auth_login_attempts.failure_reason

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\authentication\api.py", line 31, in login
    user = AuthService.authenticate_user(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\authentication\services.py", line 38, in authenticate_user
    cls._log_login_attempt(email, ip_address, user_agent, True)
  File "F:\PycharmProjects\django_ninja_cms\apps\authentication\services.py", line 179, in _log_login_attempt
    LoginAttempt.objects.create(
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
           ^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: auth_login_attempts.failure_reason
INFO 2025-07-12 21:57:38,351 logging 13376 23564 Request completed - ID: 2c3e3f36-7563-4c9e-8c1c-12d1bbc3c5f2 | Status: 500 | Duration: 1.557s
ERROR 2025-07-12 21:57:38,353 log 13376 23564 Internal Server Error: /api/auth/login
ERROR 2025-07-12 21:57:38,354 basehttp 13376 23564 "POST /api/auth/login HTTP/1.1" 500 5016
INFO 2025-07-12 21:58:12,933 autoreload 13376 33680 F:\PycharmProjects\django_ninja_cms\apps\authentication\services.py changed, reloading.
INFO 2025-07-12 21:58:15,396 autoreload 5988 30644 Watching for file changes with StatReloader
INFO 2025-07-12 21:58:48,137 logging 5988 13620 Request started - ID: 1308f408-1f60-441e-abd0-aee2b3b76c87 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 21:58:49,814 logging 5988 13620 Request completed - ID: 1308f408-1f60-441e-abd0-aee2b3b76c87 | Status: 200 | Duration: 1.677s
INFO 2025-07-12 21:58:49,815 basehttp 5988 13620 "POST /api/auth/login HTTP/1.1" 200 693
INFO 2025-07-12 21:59:07,683 logging 5988 13620 Request started - ID: df51166e-c63e-4810-be8f-3d3b7026a8c0 | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-12 21:59:07,731 logging 5988 13620 Request completed - ID: df51166e-c63e-4810-be8f-3d3b7026a8c0 | Status: 200 | Duration: 0.048s
INFO 2025-07-12 21:59:07,732 basehttp 5988 13620 "GET /api/users/me HTTP/1.1" 200 307
INFO 2025-07-12 22:01:56,907 autoreload 6860 33728 Watching for file changes with StatReloader
INFO 2025-07-12 22:02:27,552 logging 6860 31444 Request started - ID: 9845ec8c-97c6-43ce-b2b1-afd8449cd083 | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:02:27,605 logging 6860 31444 Request completed - ID: 9845ec8c-97c6-43ce-b2b1-afd8449cd083 | Status: 401 | Duration: 0.053s
WARNING 2025-07-12 22:02:27,607 log 6860 31444 Unauthorized: /api/permissions/permissions
WARNING 2025-07-12 22:02:27,609 basehttp 6860 31444 "GET /api/permissions/permissions HTTP/1.1" 401 37
INFO 2025-07-12 22:07:09,126 logging 6860 33340 Request started - ID: b3b848b0-4a6a-4d55-950a-774051cda55b | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:07:10,542 logging 6860 33340 Request completed - ID: b3b848b0-4a6a-4d55-950a-774051cda55b | Status: 200 | Duration: 1.416s
INFO 2025-07-12 22:07:10,543 basehttp 6860 33340 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-12 22:08:08,855 logging 6860 33340 Request started - ID: 5a3c3436-776c-4797-ba89-c6ec542106c6 | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:08:08,872 logging 6860 33340 Request completed - ID: 5a3c3436-776c-4797-ba89-c6ec542106c6 | Status: 401 | Duration: 0.018s
WARNING 2025-07-12 22:08:08,873 log 6860 33340 Unauthorized: /api/permissions/permissions
WARNING 2025-07-12 22:08:08,874 basehttp 6860 33340 "GET /api/permissions/permissions HTTP/1.1" 401 37
INFO 2025-07-12 22:13:52,685 autoreload 30296 17244 Watching for file changes with StatReloader
INFO 2025-07-12 22:14:33,507 logging 30296 30740 Request started - ID: b6ecd31e-8805-47e8-acfb-e13455f646eb | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:14:33,780 logging 30296 30740 Request completed - ID: b6ecd31e-8805-47e8-acfb-e13455f646eb | Status: 400 | Duration: 0.272s
WARNING 2025-07-12 22:14:33,781 log 30296 30740 Bad Request: /api/auth/register
WARNING 2025-07-12 22:14:33,782 basehttp 30296 30740 "POST /api/auth/register HTTP/1.1" 400 49
INFO 2025-07-12 22:14:43,162 logging 30296 30740 Request started - ID: e4aaeac8-d173-44b2-b1a4-ac45b29c3e81 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:14:44,292 logging 30296 30740 Request completed - ID: e4aaeac8-d173-44b2-b1a4-ac45b29c3e81 | Status: 200 | Duration: 1.130s
INFO 2025-07-12 22:14:44,293 basehttp 30296 30740 "POST /api/auth/register HTTP/1.1" 200 100
INFO 2025-07-12 22:18:50,342 logging 30296 5884 Request started - ID: e77a0dd2-7b3f-4f7a-843f-e18a2ae4526e | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:18:51,587 logging 30296 5884 Request completed - ID: e77a0dd2-7b3f-4f7a-843f-e18a2ae4526e | Status: 200 | Duration: 1.247s
INFO 2025-07-12 22:18:51,588 basehttp 30296 5884 "POST /api/auth/login HTTP/1.1" 200 695
INFO 2025-07-12 22:19:23,152 logging 30296 5884 Request started - ID: ac4b443f-2c60-4b99-b983-024e00d7b7d6 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:19:24,416 logging 30296 5884 Request completed - ID: ac4b443f-2c60-4b99-b983-024e00d7b7d6 | Status: 200 | Duration: 1.264s
INFO 2025-07-12 22:19:24,417 basehttp 30296 5884 "POST /api/auth/login HTTP/1.1" 200 695
INFO 2025-07-12 22:19:35,933 logging 30296 5884 Request started - ID: 5df49460-c901-4774-b1bd-6dc9d7d0792d | Method: GET | Path: /api/users/me | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:19:35,968 logging 30296 5884 Request completed - ID: 5df49460-c901-4774-b1bd-6dc9d7d0792d | Status: 200 | Duration: 0.035s
INFO 2025-07-12 22:19:35,968 basehttp 30296 5884 "GET /api/users/me HTTP/1.1" 200 309
INFO 2025-07-12 22:22:24,770 logging 30296 23836 Request started - ID: f9889459-72f8-40c4-805a-4c0e8c7c75d2 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:22:25,877 logging 30296 23836 Request completed - ID: f9889459-72f8-40c4-805a-4c0e8c7c75d2 | Status: 200 | Duration: 1.107s
INFO 2025-07-12 22:22:25,878 basehttp 30296 23836 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-12 22:22:38,575 logging 30296 23836 Request started - ID: 869bc1b2-9c79-4d9d-94a7-db5980f8f1da | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:22:38,591 logging 30296 23836 Request completed - ID: 869bc1b2-9c79-4d9d-94a7-db5980f8f1da | Status: 401 | Duration: 0.016s
WARNING 2025-07-12 22:22:38,592 log 30296 23836 Unauthorized: /api/permissions/permissions
WARNING 2025-07-12 22:22:38,593 basehttp 30296 23836 "GET /api/permissions/permissions HTTP/1.1" 401 37
INFO 2025-07-12 22:23:15,908 autoreload 30296 17244 F:\PycharmProjects\django_ninja_cms\apps\permissions\api.py changed, reloading.
INFO 2025-07-12 22:23:18,725 autoreload 32740 18368 Watching for file changes with StatReloader
INFO 2025-07-12 22:23:50,872 autoreload 2848 22896 Watching for file changes with StatReloader
INFO 2025-07-12 22:24:25,430 logging 2848 7648 Request started - ID: 7ac0d2c2-a76b-4cb8-a2d8-3468e4c8c424 | Method: GET | Path: /api/permissions/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:24:25,829 logging 2848 7648 Request completed - ID: 7ac0d2c2-a76b-4cb8-a2d8-3468e4c8c424 | Status: 200 | Duration: 0.399s
INFO 2025-07-12 22:24:25,830 basehttp 2848 7648 "GET /api/permissions/permissions HTTP/1.1" 200 4331
INFO 2025-07-12 22:24:36,591 logging 2848 7648 Request started - ID: 1a52ed4d-4d64-4f76-a02d-dd92af37276e | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
ERROR 2025-07-12 22:24:36,602 errors 2848 7648 1 validation error for RoleResponse
permissions
  Input should be a valid list [type=list_type, input_value=<django.db.models.fields....t at 0x000001885A2330B0>, input_type=create_forward_many_to_many_manager.<locals>.ManyRelatedManager]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\permissions\decorators.py", line 44, in wrapper
    return func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\permissions\api.py", line 48, in list_roles
    return [RoleResponse.from_orm(role) for role in roles]
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\pydantic\main.py", line 1440, in from_orm
    return cls.model_validate(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for RoleResponse
permissions
  Input should be a valid list [type=list_type, input_value=<django.db.models.fields....t at 0x000001885A2330B0>, input_type=create_forward_many_to_many_manager.<locals>.ManyRelatedManager]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
INFO 2025-07-12 22:24:36,629 logging 2848 7648 Request completed - ID: 1a52ed4d-4d64-4f76-a02d-dd92af37276e | Status: 500 | Duration: 0.039s
ERROR 2025-07-12 22:24:36,630 log 2848 7648 Internal Server Error: /api/permissions/roles
ERROR 2025-07-12 22:24:36,631 basehttp 2848 7648 "GET /api/permissions/roles HTTP/1.1" 500 1373
INFO 2025-07-12 22:25:05,808 autoreload 2848 22896 F:\PycharmProjects\django_ninja_cms\apps\permissions\api.py changed, reloading.
INFO 2025-07-12 22:25:08,331 autoreload 5080 11464 Watching for file changes with StatReloader
INFO 2025-07-12 22:26:11,831 autoreload 5660 26488 Watching for file changes with StatReloader
INFO 2025-07-12 22:27:03,242 logging 5660 21960 Request started - ID: 58d4e115-6c02-4a02-b72b-d2f2bc95cadb | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:27:05,012 logging 5660 21960 Request completed - ID: 58d4e115-6c02-4a02-b72b-d2f2bc95cadb | Status: 200 | Duration: 1.770s
INFO 2025-07-12 22:27:05,012 basehttp 5660 21960 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-12 22:27:37,251 logging 5660 21960 Request started - ID: 554e7d9b-8070-4c70-9b17-cf35de7821c4 | Method: GET | Path: /api/permissions/roles | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:27:37,296 logging 5660 21960 Request completed - ID: 554e7d9b-8070-4c70-9b17-cf35de7821c4 | Status: 200 | Duration: 0.046s
INFO 2025-07-12 22:27:37,297 basehttp 5660 21960 "GET /api/permissions/roles HTTP/1.1" 200 12512
INFO 2025-07-12 22:27:55,990 logging 5660 21960 Request started - ID: 7152396d-5ee9-4b22-bbb0-8d02831f83dd | Method: GET | Path: /api/permissions/user-permissions/3 | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:27:56,040 logging 5660 21960 Request completed - ID: 7152396d-5ee9-4b22-bbb0-8d02831f83dd | Status: 404 | Duration: 0.050s
WARNING 2025-07-12 22:27:56,041 log 5660 21960 Not Found: /api/permissions/user-permissions/3
WARNING 2025-07-12 22:27:56,042 basehttp 5660 21960 "GET /api/permissions/user-permissions/3 HTTP/1.1" 404 29649
INFO 2025-07-12 22:28:15,539 logging 5660 21960 Request started - ID: d58c771f-4a79-47a6-a608-c22f5916fa21 | Method: GET | Path: /api/permissions/users/3/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:28:15,584 logging 5660 21960 Request completed - ID: d58c771f-4a79-47a6-a608-c22f5916fa21 | Status: 200 | Duration: 0.047s
INFO 2025-07-12 22:28:15,585 basehttp 5660 21960 "GET /api/permissions/users/3/permissions HTTP/1.1" 200 401
INFO 2025-07-12 22:28:35,791 logging 5660 21960 Request started - ID: 1588cfd5-8386-4067-b2b3-c782b7a3fb72 | Method: GET | Path: /api/permissions/check-permission/users.view | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:28:35,809 logging 5660 21960 Request completed - ID: 1588cfd5-8386-4067-b2b3-c782b7a3fb72 | Status: 200 | Duration: 0.019s
INFO 2025-07-12 22:28:35,810 basehttp 5660 21960 "GET /api/permissions/check-permission/users.view HTTP/1.1" 200 66
INFO 2025-07-12 22:28:49,159 logging 5660 21960 Request started - ID: 4ad91053-813b-4506-bbdc-5814fff489d0 | Method: GET | Path: /api/users/ | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:28:49,191 logging 5660 21960 Request completed - ID: 4ad91053-813b-4506-bbdc-5814fff489d0 | Status: 200 | Duration: 0.033s
INFO 2025-07-12 22:28:49,191 basehttp 5660 21960 "GET /api/users/ HTTP/1.1" 200 1321
INFO 2025-07-12 22:29:06,999 logging 5660 21960 Request started - ID: 3a6ec2e0-2cc7-449f-868a-d1afa8a846a4 | Method: POST | Path: /api/permissions/users/assign-role | User:  | IP: 127.0.0.1
ERROR 2025-07-12 22:29:07,029 errors 5660 21960 1 validation error for UserRoleResponse
role.permissions
  Input should be a valid list [type=list_type, input_value=<django.db.models.fields....t at 0x000001F98C192480>, input_type=create_forward_many_to_many_manager.<locals>.ManyRelatedManager]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\permissions\decorators.py", line 44, in wrapper
    return func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\permissions\api.py", line 170, in assign_role_to_user
    return UserRoleResponse.from_orm(user_role)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\pydantic\main.py", line 1440, in from_orm
    return cls.model_validate(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for UserRoleResponse
role.permissions
  Input should be a valid list [type=list_type, input_value=<django.db.models.fields....t at 0x000001F98C192480>, input_type=create_forward_many_to_many_manager.<locals>.ManyRelatedManager]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
INFO 2025-07-12 22:29:07,066 logging 5660 21960 Request completed - ID: 3a6ec2e0-2cc7-449f-868a-d1afa8a846a4 | Status: 500 | Duration: 0.067s
ERROR 2025-07-12 22:29:07,066 log 5660 21960 Internal Server Error: /api/permissions/users/assign-role
ERROR 2025-07-12 22:29:07,067 basehttp 5660 21960 "POST /api/permissions/users/assign-role HTTP/1.1" 500 1389
INFO 2025-07-12 22:29:54,116 autoreload 5660 26488 F:\PycharmProjects\django_ninja_cms\apps\permissions\api.py changed, reloading.
INFO 2025-07-12 22:29:56,432 autoreload 21096 21308 Watching for file changes with StatReloader
INFO 2025-07-12 22:30:22,434 autoreload 19188 12800 Watching for file changes with StatReloader
INFO 2025-07-12 22:30:58,901 logging 19188 29808 Request started - ID: 1f25cc51-9e25-458a-bf5b-7d84d1635e94 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:31:00,419 logging 19188 29808 Request completed - ID: 1f25cc51-9e25-458a-bf5b-7d84d1635e94 | Status: 200 | Duration: 1.519s
INFO 2025-07-12 22:31:00,420 basehttp 19188 29808 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-12 22:31:34,172 logging 19188 29808 Request started - ID: 09fdd881-3ec6-4d77-b0ae-3482b3c4adfc | Method: POST | Path: /api/permissions/users/assign-role | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:31:34,269 logging 19188 29808 Request completed - ID: 09fdd881-3ec6-4d77-b0ae-3482b3c4adfc | Status: 200 | Duration: 0.097s
INFO 2025-07-12 22:31:34,270 basehttp 19188 29808 "POST /api/permissions/users/assign-role HTTP/1.1" 200 1497
INFO 2025-07-12 22:31:51,859 logging 19188 29808 Request started - ID: afd08ec6-54e1-4e31-aed2-51369010f3b0 | Method: GET | Path: /api/permissions/users/4/permissions | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:31:51,916 logging 19188 29808 Request completed - ID: afd08ec6-54e1-4e31-aed2-51369010f3b0 | Status: 200 | Duration: 0.057s
INFO 2025-07-12 22:31:51,917 basehttp 19188 29808 "GET /api/permissions/users/4/permissions HTTP/1.1" 200 141
INFO 2025-07-12 22:32:00,096 logging 19188 11628 Request started - ID: 03aa0ace-a7df-4dab-9dcf-33fcf8b7bc42 | Method: GET | Path: /api/docs | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:32:00,256 logging 19188 11628 Request completed - ID: 03aa0ace-a7df-4dab-9dcf-33fcf8b7bc42 | Status: 404 | Duration: 0.160s
WARNING 2025-07-12 22:32:00,277 log 19188 11628 Not Found: /api/docs
WARNING 2025-07-12 22:32:00,282 basehttp 19188 11628 "GET /api/docs HTTP/1.1" 404 29546
INFO 2025-07-12 22:32:00,421 basehttp 19188 11628 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-07-12 22:32:00,422 basehttp 19188 22140 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-07-12 22:32:01,838 basehttp 19188 22140 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-07-12 22:32:02,981 logging 19188 22140 Request started - ID: aa0995e6-b0a3-4400-8eb5-48f23fe49a1b | Method: GET | Path: /favicon.ico | User:  | IP: 127.0.0.1
INFO 2025-07-12 22:32:03,036 logging 19188 22140 Request completed - ID: aa0995e6-b0a3-4400-8eb5-48f23fe49a1b | Status: 404 | Duration: 0.055s
WARNING 2025-07-12 22:32:03,036 log 19188 22140 Not Found: /favicon.ico
WARNING 2025-07-12 22:32:03,037 basehttp 19188 22140 "GET /favicon.ico HTTP/1.1" 404 16119
INFO 2025-07-12 22:55:13,985 basehttp 19188 29808 - Broken pipe from ('127.0.0.1', 8405)
INFO 2025-07-13 12:44:26,834 autoreload 36084 10452 Watching for file changes with StatReloader
INFO 2025-07-13 12:45:14,290 logging 36084 33012 Request started - ID: ab1e2d01-72a1-401c-befa-b1fd9a9953d4 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:45:14,381 logging 36084 33012 Request completed - ID: ab1e2d01-72a1-401c-befa-b1fd9a9953d4 | Status: 422 | Duration: 0.091s
WARNING 2025-07-13 12:45:14,384 log 36084 33012 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:45:14,385 basehttp 36084 33012 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:45:16,406 logging 36084 35888 Request started - ID: 3f7b49fa-2c56-4969-8c10-2c649c19f757 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:45:17,542 logging 36084 35888 Request completed - ID: 3f7b49fa-2c56-4969-8c10-2c649c19f757 | Status: 401 | Duration: 1.136s
WARNING 2025-07-13 12:45:17,543 log 36084 35888 Unauthorized: /api/auth/login
WARNING 2025-07-13 12:45:17,543 basehttp 36084 35888 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-13 12:46:13,964 logging 36084 36304 Request started - ID: 8a765d83-1680-41ee-b918-bc629fdb1015 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:46:13,997 logging 36084 36304 Request completed - ID: 8a765d83-1680-41ee-b918-bc629fdb1015 | Status: 422 | Duration: 0.034s
WARNING 2025-07-13 12:46:13,998 log 36084 36304 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:46:13,999 basehttp 36084 36304 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:46:16,032 logging 36084 31648 Request started - ID: 23affc26-b2aa-4dce-9cbb-46a8c895feb4 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:46:17,009 logging 36084 31648 Request completed - ID: 23affc26-b2aa-4dce-9cbb-46a8c895feb4 | Status: 401 | Duration: 0.977s
WARNING 2025-07-13 12:46:17,010 log 36084 31648 Unauthorized: /api/auth/login
WARNING 2025-07-13 12:46:17,010 basehttp 36084 31648 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-13 12:46:46,138 logging 36084 5388 Request started - ID: 36fb9276-bfb3-4e58-a73f-03a35c6ba865 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:46:46,172 logging 36084 5388 Request completed - ID: 36fb9276-bfb3-4e58-a73f-03a35c6ba865 | Status: 422 | Duration: 0.035s
WARNING 2025-07-13 12:46:46,173 log 36084 5388 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:46:46,173 basehttp 36084 5388 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:46:48,230 logging 36084 34832 Request started - ID: e75ea137-d6dc-4419-a9f6-ce78347a349a | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:46:49,480 logging 36084 34832 Request completed - ID: e75ea137-d6dc-4419-a9f6-ce78347a349a | Status: 401 | Duration: 1.249s
WARNING 2025-07-13 12:46:49,481 log 36084 34832 Unauthorized: /api/auth/login
WARNING 2025-07-13 12:46:49,481 basehttp 36084 34832 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-13 12:47:05,994 logging 36084 37536 Request started - ID: 1a4b5d9a-1f92-4cee-bf89-3fff833c0e8e | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:47:06,014 logging 36084 37536 Request completed - ID: 1a4b5d9a-1f92-4cee-bf89-3fff833c0e8e | Status: 422 | Duration: 0.020s
WARNING 2025-07-13 12:47:06,015 log 36084 37536 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:47:06,015 basehttp 36084 37536 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:47:08,055 logging 36084 37220 Request started - ID: a2bd67bb-d02d-4e4d-bb70-02a67e894f36 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:47:09,041 logging 36084 37220 Request completed - ID: a2bd67bb-d02d-4e4d-bb70-02a67e894f36 | Status: 401 | Duration: 0.987s
WARNING 2025-07-13 12:47:09,042 log 36084 37220 Unauthorized: /api/auth/login
WARNING 2025-07-13 12:47:09,042 basehttp 36084 37220 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-13 12:50:40,187 logging 36084 37648 Request started - ID: d270a2f8-29d3-4897-a08c-6b37cc7afd04 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:50:40,231 logging 36084 37648 Request completed - ID: d270a2f8-29d3-4897-a08c-6b37cc7afd04 | Status: 422 | Duration: 0.044s
WARNING 2025-07-13 12:50:40,232 log 36084 37648 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:50:40,234 basehttp 36084 37648 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:50:42,266 logging 36084 16592 Request started - ID: 717b855b-e310-4f5a-8a86-d14eb64cbe9d | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:50:43,313 logging 36084 16592 Request completed - ID: 717b855b-e310-4f5a-8a86-d14eb64cbe9d | Status: 401 | Duration: 1.047s
WARNING 2025-07-13 12:50:43,313 log 36084 16592 Unauthorized: /api/auth/login
WARNING 2025-07-13 12:50:43,314 basehttp 36084 16592 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-13 12:51:00,752 logging 36084 15300 Request started - ID: f86f91bb-779b-4c54-9978-09d038ef2396 | Method: GET | Path: /admin/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:51:00,833 logging 36084 15300 Request completed - ID: f86f91bb-779b-4c54-9978-09d038ef2396 | Status: 302 | Duration: 0.081s
INFO 2025-07-13 12:51:00,834 basehttp 36084 15300 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-13 12:51:00,846 logging 36084 15300 Request started - ID: 04f4ac41-2a58-4641-a05d-941bd9dc92db | Method: GET | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:51:00,924 logging 36084 15300 Request completed - ID: 04f4ac41-2a58-4641-a05d-941bd9dc92db | Status: 200 | Duration: 0.078s
INFO 2025-07-13 12:51:00,926 basehttp 36084 15300 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 17343
INFO 2025-07-13 12:51:01,023 basehttp 36084 15300 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,024 basehttp 36084 37236 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,028 basehttp 36084 14212 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,031 basehttp 36084 26356 "GET /static/admin/css/login.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,032 basehttp 36084 37860 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,033 basehttp 36084 34040 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,039 basehttp 36084 26356 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-07-13 12:51:01,044 basehttp 36084 37860 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:02,167 basehttp 36084 37860 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-07-13 12:51:02,190 basehttp 36084 37860 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-07-13 12:51:02,823 logging 36084 37860 Request started - ID: b6a093d5-8252-43fc-aa76-0a312ed782f9 | Method: GET | Path: /favicon.ico | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:51:02,919 logging 36084 37860 Request completed - ID: b6a093d5-8252-43fc-aa76-0a312ed782f9 | Status: 404 | Duration: 0.096s
WARNING 2025-07-13 12:51:02,945 log 36084 37860 Not Found: /favicon.ico
WARNING 2025-07-13 12:51:02,946 basehttp 36084 37860 "GET /favicon.ico HTTP/1.1" 404 16119
INFO 2025-07-13 12:51:11,052 logging 36084 37860 Request started - ID: 0926efdf-b55a-4b01-8990-666fa02d2590 | Method: POST | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:51:12,086 logging 36084 37860 Request completed - ID: 0926efdf-b55a-4b01-8990-666fa02d2590 | Status: 302 | Duration: 1.034s
INFO 2025-07-13 12:51:12,087 basehttp 36084 37860 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-13 12:51:12,099 logging 36084 37860 Request started - ID: afee263f-9e4e-40f4-8167-6126ae55a3cd | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 12:51:12,172 logging 36084 37860 Request completed - ID: afee263f-9e4e-40f4-8167-6126ae55a3cd | Status: 200 | Duration: 0.076s
INFO 2025-07-13 12:51:12,173 basehttp 36084 37860 "GET /admin/ HTTP/1.1" 200 30296
INFO 2025-07-13 12:51:12,217 basehttp 36084 37860 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-13 12:51:12,432 basehttp 36084 37860 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-13 12:51:12,432 basehttp 36084 34040 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-07-13 12:51:12,433 basehttp 36084 26356 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-07-13 12:53:14,171 logging 36084 30740 Request started - ID: 0ef55893-7062-4ce7-b591-32b8ab11136b | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:14,184 logging 36084 30740 Request completed - ID: 0ef55893-7062-4ce7-b591-32b8ab11136b | Status: 422 | Duration: 0.013s
WARNING 2025-07-13 12:53:14,184 log 36084 30740 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:53:14,185 basehttp 36084 30740 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:53:16,258 logging 36084 37488 Request started - ID: 32830d7b-771b-408d-808d-f18b2ceb39f1 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:17,263 logging 36084 37488 Request completed - ID: 32830d7b-771b-408d-808d-f18b2ceb39f1 | Status: 401 | Duration: 1.014s
WARNING 2025-07-13 12:53:17,263 log 36084 37488 Unauthorized: /api/auth/login
WARNING 2025-07-13 12:53:17,264 basehttp 36084 37488 "POST /api/auth/login HTTP/1.1" 401 51
INFO 2025-07-13 12:53:38,935 logging 36084 11108 Request started - ID: e070594d-48ff-4025-899c-46d77ad9bbbe | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:38,976 logging 36084 11108 Request completed - ID: e070594d-48ff-4025-899c-46d77ad9bbbe | Status: 422 | Duration: 0.041s
WARNING 2025-07-13 12:53:38,977 log 36084 11108 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 12:53:38,979 basehttp 36084 11108 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 12:53:41,008 logging 36084 11904 Request started - ID: 930b2432-f605-464d-8a69-5cb467e1b6d8 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:42,163 logging 36084 11904 Request completed - ID: 930b2432-f605-464d-8a69-5cb467e1b6d8 | Status: 200 | Duration: 1.155s
INFO 2025-07-13 12:53:42,164 basehttp 36084 11904 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 12:53:44,209 logging 36084 36692 Request started - ID: 8783613a-931f-4bf3-b25d-ccb436a6d832 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:44,294 logging 36084 36692 Request completed - ID: 8783613a-931f-4bf3-b25d-ccb436a6d832 | Status: 500 | Duration: 0.084s
ERROR 2025-07-13 12:53:44,295 log 36084 36692 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 12:53:44,298 basehttp 36084 36692 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 12:53:44,306 logging 36084 36692 Request started - ID: a4af6503-fa80-45df-bff1-9fbacdb8c91c | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:44,361 logging 36084 36692 Request completed - ID: a4af6503-fa80-45df-bff1-9fbacdb8c91c | Status: 500 | Duration: 0.057s
ERROR 2025-07-13 12:53:44,362 log 36084 36692 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 12:53:44,363 basehttp 36084 36692 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 12:53:46,403 logging 36084 37196 Request started - ID: 33e0a5f3-fba1-48de-ab4a-1041b4dd03ac | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 12:53:46,446 logging 36084 37196 Request completed - ID: 33e0a5f3-fba1-48de-ab4a-1041b4dd03ac | Status: 404 | Duration: 0.043s
WARNING 2025-07-13 12:53:46,447 log 36084 37196 Not Found: /api/docs/
WARNING 2025-07-13 12:53:46,448 basehttp 36084 37196 "GET /api/docs/ HTTP/1.1" 404 29549
INFO 2025-07-13 13:12:16,017 logging 36084 20468 Request started - ID: 7e5802d5-ec78-4408-a687-2645f6917874 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:12:16,037 logging 36084 20468 Request completed - ID: 7e5802d5-ec78-4408-a687-2645f6917874 | Status: 422 | Duration: 0.020s
WARNING 2025-07-13 13:12:16,038 log 36084 20468 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:12:16,038 basehttp 36084 20468 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:12:18,077 logging 36084 35168 Request started - ID: 405cdef2-4e83-4e03-b700-69888a6609a9 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:12:19,670 logging 36084 35168 Request completed - ID: 405cdef2-4e83-4e03-b700-69888a6609a9 | Status: 200 | Duration: 1.593s
INFO 2025-07-13 13:12:19,671 basehttp 36084 35168 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:12:21,710 logging 36084 35692 Request started - ID: cfe7f3e2-139a-4ace-af24-be88bc7b387d | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:12:21,733 logging 36084 35692 Request completed - ID: cfe7f3e2-139a-4ace-af24-be88bc7b387d | Status: 500 | Duration: 0.023s
ERROR 2025-07-13 13:12:21,735 log 36084 35692 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:12:21,736 basehttp 36084 35692 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:12:21,740 logging 36084 35692 Request started - ID: 886ef10a-982d-451f-a85b-add29b5a6d51 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:12:21,766 logging 36084 35692 Request completed - ID: 886ef10a-982d-451f-a85b-add29b5a6d51 | Status: 500 | Duration: 0.026s
ERROR 2025-07-13 13:12:21,767 log 36084 35692 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:12:21,767 basehttp 36084 35692 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:12:23,815 logging 36084 28720 Request started - ID: d0829cd6-6200-44bb-a1b7-077a301c2d31 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:12:23,914 logging 36084 28720 Request completed - ID: d0829cd6-6200-44bb-a1b7-077a301c2d31 | Status: 404 | Duration: 0.099s
WARNING 2025-07-13 13:12:23,915 log 36084 28720 Not Found: /api/docs/
WARNING 2025-07-13 13:12:23,915 basehttp 36084 28720 "GET /api/docs/ HTTP/1.1" 404 29549
INFO 2025-07-13 13:13:05,020 logging 36084 35592 Request started - ID: 9958c445-b9e8-4d3b-b054-03352c2df149 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:13:05,063 logging 36084 35592 Request completed - ID: 9958c445-b9e8-4d3b-b054-03352c2df149 | Status: 422 | Duration: 0.043s
WARNING 2025-07-13 13:13:05,064 log 36084 35592 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:13:05,065 basehttp 36084 35592 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:13:07,092 logging 36084 37428 Request started - ID: 247604b6-7f8d-4244-bda8-71df139537e2 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:13:08,139 logging 36084 37428 Request completed - ID: 247604b6-7f8d-4244-bda8-71df139537e2 | Status: 200 | Duration: 1.047s
INFO 2025-07-13 13:13:08,141 basehttp 36084 37428 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:13:10,179 logging 36084 8816 Request started - ID: fe91bdc1-0b62-42c1-b2ed-a2cb138a5e95 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:13:10,227 logging 36084 8816 Request completed - ID: fe91bdc1-0b62-42c1-b2ed-a2cb138a5e95 | Status: 500 | Duration: 0.048s
ERROR 2025-07-13 13:13:10,228 log 36084 8816 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:13:10,229 basehttp 36084 8816 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:13:10,233 logging 36084 8816 Request started - ID: d21a824a-8bba-4023-8f15-b815c2365369 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:13:10,262 logging 36084 8816 Request completed - ID: d21a824a-8bba-4023-8f15-b815c2365369 | Status: 500 | Duration: 0.029s
ERROR 2025-07-13 13:13:10,263 log 36084 8816 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:13:10,264 basehttp 36084 8816 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:13:12,319 logging 36084 19556 Request started - ID: d11fb857-658f-4e62-9103-76ddeee5f669 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:13:12,402 logging 36084 19556 Request completed - ID: d11fb857-658f-4e62-9103-76ddeee5f669 | Status: 404 | Duration: 0.084s
WARNING 2025-07-13 13:13:12,403 log 36084 19556 Not Found: /api/docs/
WARNING 2025-07-13 13:13:12,403 basehttp 36084 19556 "GET /api/docs/ HTTP/1.1" 404 29549
INFO 2025-07-13 13:14:46,295 autoreload 36084 10452 F:\PycharmProjects\django_ninja_cms\apps\storage\models.py changed, reloading.
INFO 2025-07-13 13:14:48,396 autoreload 37332 26640 Watching for file changes with StatReloader
INFO 2025-07-13 13:15:41,821 logging 37332 12904 Request started - ID: ea76ef2b-4d96-4cb9-b882-b46abb28adbe | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:15:41,847 logging 37332 12904 Request completed - ID: ea76ef2b-4d96-4cb9-b882-b46abb28adbe | Status: 422 | Duration: 0.026s
WARNING 2025-07-13 13:15:41,849 log 37332 12904 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:15:41,849 basehttp 37332 12904 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:15:43,882 logging 37332 20020 Request started - ID: 477e237a-d71c-49e9-9891-deb61cc25405 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:15:45,180 logging 37332 20020 Request completed - ID: 477e237a-d71c-49e9-9891-deb61cc25405 | Status: 200 | Duration: 1.298s
INFO 2025-07-13 13:15:45,181 basehttp 37332 20020 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:15:47,232 logging 37332 26212 Request started - ID: 57e9ba21-a6e2-4740-a212-7af70773944f | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:15:47,272 logging 37332 26212 Request completed - ID: 57e9ba21-a6e2-4740-a212-7af70773944f | Status: 400 | Duration: 0.040s
WARNING 2025-07-13 13:15:47,273 log 37332 26212 Bad Request: /api/storage/upload
WARNING 2025-07-13 13:15:47,274 basehttp 37332 26212 "POST /api/storage/upload HTTP/1.1" 400 242
INFO 2025-07-13 13:15:47,277 logging 37332 26212 Request started - ID: fcd595f9-d517-4eb0-bb83-7fa72febae8e | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:15:47,310 logging 37332 26212 Request completed - ID: fcd595f9-d517-4eb0-bb83-7fa72febae8e | Status: 400 | Duration: 0.033s
WARNING 2025-07-13 13:15:47,311 log 37332 26212 Bad Request: /api/storage/upload
WARNING 2025-07-13 13:15:47,311 basehttp 37332 26212 "POST /api/storage/upload HTTP/1.1" 400 248
INFO 2025-07-13 13:15:49,352 logging 37332 31148 Request started - ID: 449b91f6-5747-45e5-8f3f-c7de5e87dbfd | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:15:49,390 logging 37332 31148 Request completed - ID: 449b91f6-5747-45e5-8f3f-c7de5e87dbfd | Status: 404 | Duration: 0.039s
WARNING 2025-07-13 13:15:49,390 log 37332 31148 Not Found: /api/docs/
WARNING 2025-07-13 13:15:49,391 basehttp 37332 31148 "GET /api/docs/ HTTP/1.1" 404 29549
INFO 2025-07-13 13:16:14,368 autoreload 14260 34392 Watching for file changes with StatReloader
INFO 2025-07-13 13:16:20,547 logging 14260 37120 Request started - ID: b1de28a8-c073-4573-8d2d-945baa637b10 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:20,593 logging 14260 37120 Request completed - ID: b1de28a8-c073-4573-8d2d-945baa637b10 | Status: 422 | Duration: 0.046s
WARNING 2025-07-13 13:16:20,594 log 14260 37120 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:16:20,595 basehttp 14260 37120 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:16:22,635 logging 14260 34404 Request started - ID: 410df89d-772f-4a69-bc3b-25c044824700 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:23,869 logging 14260 34404 Request completed - ID: 410df89d-772f-4a69-bc3b-25c044824700 | Status: 200 | Duration: 1.234s
INFO 2025-07-13 13:16:23,887 basehttp 14260 34404 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:16:25,923 logging 14260 8368 Request started - ID: 19835095-ff8a-40d8-b980-0c0e4c3a777d | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:25,958 logging 14260 8368 Request completed - ID: 19835095-ff8a-40d8-b980-0c0e4c3a777d | Status: 500 | Duration: 0.035s
ERROR 2025-07-13 13:16:25,959 log 14260 8368 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:16:25,959 basehttp 14260 8368 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:16:25,962 logging 14260 8368 Request started - ID: f52a141f-e133-4364-a238-fc3ca6c91990 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:25,990 logging 14260 8368 Request completed - ID: f52a141f-e133-4364-a238-fc3ca6c91990 | Status: 500 | Duration: 0.029s
ERROR 2025-07-13 13:16:25,991 log 14260 8368 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:16:25,991 basehttp 14260 8368 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:16:28,013 logging 14260 10448 Request started - ID: 6f434e09-17fc-4043-a8b0-ce97782a4c18 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:28,159 logging 14260 10448 Request completed - ID: 6f434e09-17fc-4043-a8b0-ce97782a4c18 | Status: 404 | Duration: 0.148s
WARNING 2025-07-13 13:16:28,160 log 14260 10448 Not Found: /api/docs/
WARNING 2025-07-13 13:16:28,162 basehttp 14260 10448 "GET /api/docs/ HTTP/1.1" 404 29550
INFO 2025-07-13 13:16:49,296 logging 14260 23368 Request started - ID: be27e24e-f944-47a7-8ff3-eb404203c4bd | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:49,349 logging 14260 23368 Request completed - ID: be27e24e-f944-47a7-8ff3-eb404203c4bd | Status: 422 | Duration: 0.053s
WARNING 2025-07-13 13:16:49,350 log 14260 23368 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:16:49,353 basehttp 14260 23368 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:16:51,415 logging 14260 33660 Request started - ID: d298cc1a-b76a-4e9a-aa46-e65126424a6f | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:52,439 logging 14260 33660 Request completed - ID: d298cc1a-b76a-4e9a-aa46-e65126424a6f | Status: 200 | Duration: 1.025s
INFO 2025-07-13 13:16:52,440 basehttp 14260 33660 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:16:54,465 logging 14260 37764 Request started - ID: 219f02a1-1baa-4615-8bf5-a03566a690ae | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:54,497 logging 14260 37764 Request completed - ID: 219f02a1-1baa-4615-8bf5-a03566a690ae | Status: 500 | Duration: 0.032s
ERROR 2025-07-13 13:16:54,498 log 14260 37764 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:16:54,499 basehttp 14260 37764 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:16:54,503 logging 14260 37764 Request started - ID: 57c8b755-8a54-454b-ac19-976f1220169a | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:54,541 logging 14260 37764 Request completed - ID: 57c8b755-8a54-454b-ac19-976f1220169a | Status: 500 | Duration: 0.038s
ERROR 2025-07-13 13:16:54,542 log 14260 37764 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:16:54,543 basehttp 14260 37764 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:16:56,600 logging 14260 18648 Request started - ID: 67d357a8-3981-4d06-8f1c-3652604fdf60 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:16:56,663 logging 14260 18648 Request completed - ID: 67d357a8-3981-4d06-8f1c-3652604fdf60 | Status: 404 | Duration: 0.062s
WARNING 2025-07-13 13:16:56,663 log 14260 18648 Not Found: /api/docs/
WARNING 2025-07-13 13:16:56,664 basehttp 14260 18648 "GET /api/docs/ HTTP/1.1" 404 29549
INFO 2025-07-13 13:18:17,633 autoreload 14260 34392 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:18:19,673 autoreload 35700 17804 Watching for file changes with StatReloader
INFO 2025-07-13 13:18:37,619 logging 35700 36960 Request started - ID: 15bc2c3a-dbfd-44dc-9675-4e6410a23a2a | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:18:37,662 logging 35700 36960 Request completed - ID: 15bc2c3a-dbfd-44dc-9675-4e6410a23a2a | Status: 422 | Duration: 0.044s
WARNING 2025-07-13 13:18:37,663 log 35700 36960 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:18:37,663 basehttp 35700 36960 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:18:39,694 logging 35700 3196 Request started - ID: 948e4b11-feb6-47fa-aea4-7409ecafdbce | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:18:40,969 logging 35700 3196 Request completed - ID: 948e4b11-feb6-47fa-aea4-7409ecafdbce | Status: 200 | Duration: 1.274s
INFO 2025-07-13 13:18:40,969 basehttp 35700 3196 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:18:43,001 logging 35700 25888 Request started - ID: a0f092eb-853b-4bfa-8937-8e691cff4da2 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:18:43,064 logging 35700 25888 Request completed - ID: a0f092eb-853b-4bfa-8937-8e691cff4da2 | Status: 500 | Duration: 0.064s
ERROR 2025-07-13 13:18:43,065 log 35700 25888 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:18:43,065 basehttp 35700 25888 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:18:43,069 logging 35700 25888 Request started - ID: aa5b1162-15db-4f16-84cb-f8ddca329eed | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:18:43,096 logging 35700 25888 Request completed - ID: aa5b1162-15db-4f16-84cb-f8ddca329eed | Status: 500 | Duration: 0.027s
ERROR 2025-07-13 13:18:43,096 log 35700 25888 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:18:43,097 basehttp 35700 25888 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:18:45,145 logging 35700 25748 Request started - ID: 7743a566-02d5-4d8a-858c-3aa2ef67ca4f | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:18:45,172 logging 35700 25748 Request completed - ID: 7743a566-02d5-4d8a-858c-3aa2ef67ca4f | Status: 200 | Duration: 0.027s
INFO 2025-07-13 13:18:45,173 basehttp 35700 25748 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:19:50,110 logging 35700 29964 Request started - ID: 4eb68e20-785c-4794-99ae-cb43dd5d2f2f | Method: GET | Path: /docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:19:50,156 logging 35700 29964 Request completed - ID: 4eb68e20-785c-4794-99ae-cb43dd5d2f2f | Status: 404 | Duration: 0.046s
WARNING 2025-07-13 13:19:50,156 log 35700 29964 Not Found: /docs/
WARNING 2025-07-13 13:19:50,157 basehttp 35700 29964 "GET /docs/ HTTP/1.1" 404 16095
INFO 2025-07-13 13:19:50,242 basehttp 35700 29964 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-07-13 13:19:50,244 basehttp 35700 28932 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-07-13 13:19:51,297 basehttp 35700 28932 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-07-13 13:19:52,158 logging 35700 28932 Request started - ID: 6fcbb649-fa79-4d2c-867d-ae2bab2a1411 | Method: GET | Path: /favicon.ico | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:19:52,202 logging 35700 28932 Request completed - ID: 6fcbb649-fa79-4d2c-867d-ae2bab2a1411 | Status: 404 | Duration: 0.044s
WARNING 2025-07-13 13:19:52,202 log 35700 28932 Not Found: /favicon.ico
WARNING 2025-07-13 13:19:52,203 basehttp 35700 28932 "GET /favicon.ico HTTP/1.1" 404 16119
INFO 2025-07-13 13:20:03,222 autoreload 33888 5004 Watching for file changes with StatReloader
INFO 2025-07-13 13:20:03,976 logging 33888 33976 Request started - ID: c16e7a09-44ae-4358-8ec1-07d86881d61a | Method: GET | Path: /docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:20:04,050 logging 33888 33976 Request completed - ID: c16e7a09-44ae-4358-8ec1-07d86881d61a | Status: 404 | Duration: 0.074s
WARNING 2025-07-13 13:20:04,052 log 33888 33976 Not Found: /docs/
WARNING 2025-07-13 13:20:04,053 basehttp 33888 33976 "GET /docs/ HTTP/1.1" 404 16095
INFO 2025-07-13 13:20:52,620 logging 33888 33976 Request started - ID: d259a1ae-1b5c-418e-b6da-e28ab236da08 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:20:52,705 logging 33888 33976 Request completed - ID: d259a1ae-1b5c-418e-b6da-e28ab236da08 | Status: 200 | Duration: 0.085s
INFO 2025-07-13 13:20:52,706 basehttp 33888 33976 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:20:52,872 basehttp 33888 29584 "GET /static/ninja/swagger-ui-init.js HTTP/1.1" 200 1308
INFO 2025-07-13 13:20:52,873 basehttp 33888 33976 "GET /static/ninja/swagger-ui.css HTTP/1.1" 200 151211
INFO 2025-07-13 13:20:52,910 basehttp 33888 20668 "GET /static/ninja/swagger-ui-bundle.js HTTP/1.1" 200 1385226
INFO 2025-07-13 13:20:54,175 logging 33888 20668 Request started - ID: 251f33f0-4f49-428f-9aaf-b22dcf60f377 | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:20:54,272 logging 33888 20668 Request completed - ID: 251f33f0-4f49-428f-9aaf-b22dcf60f377 | Status: 200 | Duration: 0.096s
INFO 2025-07-13 13:20:54,273 basehttp 33888 20668 "GET /api/api/openapi.json HTTP/1.1" 200 39954
INFO 2025-07-13 13:20:55,774 basehttp 33888 20668 "GET /static/ninja/favicon.png HTTP/1.1" 200 6234
INFO 2025-07-13 13:21:34,752 autoreload 33888 5004 F:\PycharmProjects\django_ninja_cms\apps\storage\services.py changed, reloading.
INFO 2025-07-13 13:21:36,701 autoreload 34416 26076 Watching for file changes with StatReloader
INFO 2025-07-13 13:22:04,061 logging 34416 27396 Request started - ID: c030ad99-2119-4ca0-bcb1-a32a9fffe26f | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:04,093 logging 34416 27396 Request completed - ID: c030ad99-2119-4ca0-bcb1-a32a9fffe26f | Status: 422 | Duration: 0.034s
WARNING 2025-07-13 13:22:04,095 log 34416 27396 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:22:04,096 basehttp 34416 27396 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:22:06,135 logging 34416 15336 Request started - ID: 7020aec6-5cfe-40fb-97dc-da9c1ba8e07a | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:07,339 logging 34416 15336 Request completed - ID: 7020aec6-5cfe-40fb-97dc-da9c1ba8e07a | Status: 200 | Duration: 1.205s
INFO 2025-07-13 13:22:07,340 basehttp 34416 15336 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:22:09,359 logging 34416 33908 Request started - ID: 449638db-c5d0-4397-9eb6-d12cfbc95f59 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:09,403 logging 34416 33908 Request completed - ID: 449638db-c5d0-4397-9eb6-d12cfbc95f59 | Status: 500 | Duration: 0.044s
ERROR 2025-07-13 13:22:09,403 log 34416 33908 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:22:09,404 basehttp 34416 33908 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:22:09,407 logging 34416 33908 Request started - ID: 3379f764-c596-4931-8db2-060cb24ecc48 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:09,442 logging 34416 33908 Request completed - ID: 3379f764-c596-4931-8db2-060cb24ecc48 | Status: 500 | Duration: 0.035s
ERROR 2025-07-13 13:22:09,443 log 34416 33908 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:22:09,444 basehttp 34416 33908 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:22:11,482 logging 34416 35084 Request started - ID: ced899c2-d988-4f70-b196-2ffee9672764 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:11,506 logging 34416 35084 Request completed - ID: ced899c2-d988-4f70-b196-2ffee9672764 | Status: 200 | Duration: 0.024s
INFO 2025-07-13 13:22:11,506 basehttp 34416 35084 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:22:19,733 logging 34416 33192 Request started - ID: ca1ed50f-0c1d-49e6-b4cc-99a66d1944a2 | Method: GET | Path: /docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:19,787 logging 34416 33192 Request completed - ID: ca1ed50f-0c1d-49e6-b4cc-99a66d1944a2 | Status: 404 | Duration: 0.054s
WARNING 2025-07-13 13:22:19,788 log 34416 33192 Not Found: /docs/
WARNING 2025-07-13 13:22:19,789 basehttp 34416 33192 "GET /docs/ HTTP/1.1" 404 16095
INFO 2025-07-13 13:22:26,593 logging 34416 33192 Request started - ID: 3840f15b-f6b7-4b66-b443-537649a177cd | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:26,631 logging 34416 33192 Request completed - ID: 3840f15b-f6b7-4b66-b443-537649a177cd | Status: 200 | Duration: 0.038s
INFO 2025-07-13 13:22:26,632 basehttp 34416 33192 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:22:27,092 logging 34416 33192 Request started - ID: 5602e5e5-b1c0-413f-b435-e9845fb58290 | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:22:27,178 logging 34416 33192 Request completed - ID: 5602e5e5-b1c0-413f-b435-e9845fb58290 | Status: 200 | Duration: 0.086s
INFO 2025-07-13 13:22:27,180 basehttp 34416 33192 "GET /api/api/openapi.json HTTP/1.1" 200 39954
INFO 2025-07-13 13:23:39,772 logging 34416 33192 Request started - ID: 11bedc06-ed04-4db5-9e27-aab9afcfbcd9 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:23:39,796 logging 34416 33192 Request completed - ID: 11bedc06-ed04-4db5-9e27-aab9afcfbcd9 | Status: 401 | Duration: 0.025s
WARNING 2025-07-13 13:23:39,809 log 34416 33192 Unauthorized: /api/storage/upload
WARNING 2025-07-13 13:23:39,809 basehttp 34416 33192 "POST /api/storage/upload HTTP/1.1" 401 26
INFO 2025-07-13 13:24:23,181 logging 34416 35324 Request started - ID: 38967072-8176-49d9-97b7-7d237dc8e041 | Method: GET | Path: /admin/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:24:23,249 logging 34416 35324 Request completed - ID: 38967072-8176-49d9-97b7-7d237dc8e041 | Status: 302 | Duration: 0.070s
INFO 2025-07-13 13:24:23,250 basehttp 34416 35324 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-13 13:24:23,264 logging 34416 35324 Request started - ID: 79550b6f-e873-42ad-bdab-dbbf449b5e6a | Method: GET | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:24:23,360 logging 34416 35324 Request completed - ID: 79550b6f-e873-42ad-bdab-dbbf449b5e6a | Status: 200 | Duration: 0.097s
INFO 2025-07-13 13:24:23,364 basehttp 34416 35324 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 17343
INFO 2025-07-13 13:24:25,673 logging 34416 35324 Request started - ID: 84278507-0fae-403d-a61c-a73ee035ad4f | Method: POST | Path: /admin/login/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:24:26,832 logging 34416 35324 Request completed - ID: 84278507-0fae-403d-a61c-a73ee035ad4f | Status: 302 | Duration: 1.159s
INFO 2025-07-13 13:24:26,833 basehttp 34416 35324 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-13 13:24:26,844 logging 34416 35324 Request started - ID: 327f302a-fa73-4328-a8fe-eb6c44afd471 | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 13:24:26,922 logging 34416 35324 Request completed - ID: 327f302a-fa73-4328-a8fe-eb6c44afd471 | Status: 200 | Duration: 0.081s
INFO 2025-07-13 13:24:26,923 basehttp 34416 35324 "GET /admin/ HTTP/1.1" 200 30296
INFO 2025-07-13 13:24:29,866 logging 34416 35324 Request started - ID: 08078b3e-98db-4173-9cfe-843c9f891518 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 13:24:29,979 logging 34416 35324 Request completed - ID: 08078b3e-98db-4173-9cfe-843c9f891518 | Status: 404 | Duration: 0.118s
WARNING 2025-07-13 13:24:29,981 log 34416 35324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-07-13 13:24:29,988 basehttp 34416 35324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 16267
INFO 2025-07-13 13:24:33,058 logging 34416 35324 Request started - ID: 665bd4bd-d9ff-4cfc-b2bf-de8f1e26c76b | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 13:24:33,172 logging 34416 35324 Request completed - ID: 665bd4bd-d9ff-4cfc-b2bf-de8f1e26c76b | Status: 200 | Duration: 0.124s
INFO 2025-07-13 13:24:33,173 basehttp 34416 35324 "GET /admin/ HTTP/1.1" 200 30297
INFO 2025-07-13 13:24:33,236 logging 34416 35324 Request started - ID: 740f6cd4-17d4-40fb-8315-0e7bb0aff0f8 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 13:24:33,333 logging 34416 35324 Request completed - ID: 740f6cd4-17d4-40fb-8315-0e7bb0aff0f8 | Status: 404 | Duration: 0.100s
WARNING 2025-07-13 13:24:33,335 log 34416 35324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-07-13 13:24:33,335 basehttp 34416 35324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 16267
INFO 2025-07-13 13:24:54,086 logging 34416 35324 Request started - ID: 473325b0-f4d2-44cc-ac41-caef91211fd8 | Method: GET | Path: /admin/ | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 13:24:54,175 logging 34416 35324 Request completed - ID: 473325b0-f4d2-44cc-ac41-caef91211fd8 | Status: 200 | Duration: 0.094s
INFO 2025-07-13 13:24:54,178 basehttp 34416 35324 "GET /admin/ HTTP/1.1" 200 30296
INFO 2025-07-13 13:24:54,254 logging 34416 35324 Request started - ID: db82d7aa-6ed5-4ad4-bbe9-0916578000d4 | Method: GET | Path: /.well-known/appspecific/com.chrome.devtools.json | User: yangjian | IP: 127.0.0.1
INFO 2025-07-13 13:24:54,328 logging 34416 35324 Request completed - ID: db82d7aa-6ed5-4ad4-bbe9-0916578000d4 | Status: 404 | Duration: 0.079s
WARNING 2025-07-13 13:24:54,330 log 34416 35324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-07-13 13:24:54,330 basehttp 34416 35324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 16267
INFO 2025-07-13 13:26:09,361 logging 34416 21960 Request started - ID: d4369a32-b787-477b-99c2-fdce447b82c7 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:09,386 logging 34416 21960 Request completed - ID: d4369a32-b787-477b-99c2-fdce447b82c7 | Status: 422 | Duration: 0.025s
WARNING 2025-07-13 13:26:09,387 log 34416 21960 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:26:09,388 basehttp 34416 21960 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:26:11,433 logging 34416 26688 Request started - ID: e5725280-cacd-4d95-9ad2-14722e63e936 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:12,521 logging 34416 26688 Request completed - ID: e5725280-cacd-4d95-9ad2-14722e63e936 | Status: 200 | Duration: 1.089s
INFO 2025-07-13 13:26:12,522 basehttp 34416 26688 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:26:29,630 logging 34416 31380 Request started - ID: d509161b-5887-4162-92f3-08d0200afc95 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:29,673 logging 34416 31380 Request completed - ID: d509161b-5887-4162-92f3-08d0200afc95 | Status: 422 | Duration: 0.043s
WARNING 2025-07-13 13:26:29,674 log 34416 31380 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:26:29,675 basehttp 34416 31380 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:26:31,737 logging 34416 35272 Request started - ID: d4c18ff8-20f3-4bf1-b003-64db6b6b8b7e | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:32,753 logging 34416 35272 Request completed - ID: d4c18ff8-20f3-4bf1-b003-64db6b6b8b7e | Status: 200 | Duration: 1.016s
INFO 2025-07-13 13:26:32,754 basehttp 34416 35272 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:26:34,803 logging 34416 4404 Request started - ID: fef8cfc7-4f62-4497-9df0-086f8b39bac7 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:34,828 logging 34416 4404 Request completed - ID: fef8cfc7-4f62-4497-9df0-086f8b39bac7 | Status: 500 | Duration: 0.025s
ERROR 2025-07-13 13:26:34,828 log 34416 4404 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:26:34,829 basehttp 34416 4404 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:26:34,832 logging 34416 4404 Request started - ID: a3b5dd3f-ed48-4c0c-bb79-55b2789261a6 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:34,869 logging 34416 4404 Request completed - ID: a3b5dd3f-ed48-4c0c-bb79-55b2789261a6 | Status: 500 | Duration: 0.037s
ERROR 2025-07-13 13:26:34,869 log 34416 4404 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:26:34,870 basehttp 34416 4404 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:26:36,911 logging 34416 32796 Request started - ID: de9dab40-4ad5-421e-a9de-a87908979662 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:36,967 logging 34416 32796 Request completed - ID: de9dab40-4ad5-421e-a9de-a87908979662 | Status: 200 | Duration: 0.056s
INFO 2025-07-13 13:26:36,970 basehttp 34416 32796 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:26:52,507 logging 34416 33192 Request started - ID: eaea3032-4d2e-410f-b9f3-6bb0c3e6c87b | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:26:52,577 logging 34416 33192 Request completed - ID: eaea3032-4d2e-410f-b9f3-6bb0c3e6c87b | Status: 200 | Duration: 0.071s
INFO 2025-07-13 13:26:52,578 basehttp 34416 33192 "POST /api/storage/upload HTTP/1.1" 200 404
INFO 2025-07-13 13:28:19,531 logging 34416 33192 Request started - ID: e26d0b25-67c6-45d4-8a52-5f31d2b4e872 | Method: GET | Path: /api/storage/files/ef5d8832-d30f-4f87-98e8-b0d4f28e5f46/download | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:28:19,533 errors 34416 33192 'WSGIRequest' object has no attribute 'auth'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 129, in download_file
    uploaded_by=request.auth
                ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'auth'
INFO 2025-07-13 13:28:19,572 logging 34416 33192 Request completed - ID: e26d0b25-67c6-45d4-8a52-5f31d2b4e872 | Status: 500 | Duration: 0.042s
ERROR 2025-07-13 13:28:19,572 log 34416 33192 Internal Server Error: /api/storage/files/ef5d8832-d30f-4f87-98e8-b0d4f28e5f46/download
ERROR 2025-07-13 13:28:19,574 basehttp 34416 33192 "GET /api/storage/files/ef5d8832-d30f-4f87-98e8-b0d4f28e5f46/download HTTP/1.1" 500 446
INFO 2025-07-13 13:28:42,565 logging 34416 20208 Request started - ID: 942dcaa6-d8ab-4502-a754-1a4e14f97f59 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:28:42,592 logging 34416 20208 Request completed - ID: 942dcaa6-d8ab-4502-a754-1a4e14f97f59 | Status: 422 | Duration: 0.026s
WARNING 2025-07-13 13:28:42,592 log 34416 20208 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:28:42,594 basehttp 34416 20208 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:28:44,627 logging 34416 7792 Request started - ID: 5dc0a023-375f-4520-a6ca-ad499f1193b3 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:28:46,005 logging 34416 7792 Request completed - ID: 5dc0a023-375f-4520-a6ca-ad499f1193b3 | Status: 200 | Duration: 1.378s
INFO 2025-07-13 13:28:46,006 basehttp 34416 7792 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:28:48,045 logging 34416 10136 Request started - ID: c5f76096-df82-4959-8dee-eb638c584c55 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:28:48,107 logging 34416 10136 Request completed - ID: c5f76096-df82-4959-8dee-eb638c584c55 | Status: 500 | Duration: 0.063s
ERROR 2025-07-13 13:28:48,108 log 34416 10136 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:28:48,108 basehttp 34416 10136 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:28:48,111 logging 34416 10136 Request started - ID: 0d677b31-3fd4-4272-8809-252b0b0d6bae | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:28:48,142 logging 34416 10136 Request completed - ID: 0d677b31-3fd4-4272-8809-252b0b0d6bae | Status: 500 | Duration: 0.029s
ERROR 2025-07-13 13:28:48,142 log 34416 10136 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:28:48,143 basehttp 34416 10136 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:28:50,178 logging 34416 37492 Request started - ID: 9ba1b38c-07b4-416c-87f5-bb66bbdda4f4 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:28:50,192 logging 34416 37492 Request completed - ID: 9ba1b38c-07b4-416c-87f5-bb66bbdda4f4 | Status: 200 | Duration: 0.014s
INFO 2025-07-13 13:28:50,193 basehttp 34416 37492 "GET /api/docs/ HTTP/1.1" 200 13773
INFO 2025-07-13 13:30:11,978 logging 34416 37488 Request started - ID: 031e5c66-4db0-4027-8355-09996e0abcb6 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:30:12,026 logging 34416 37488 Request completed - ID: 031e5c66-4db0-4027-8355-09996e0abcb6 | Status: 200 | Duration: 0.049s
INFO 2025-07-13 13:30:12,028 basehttp 34416 37488 "POST /api/storage/upload HTTP/1.1" 200 409
INFO 2025-07-13 13:30:14,080 logging 34416 23036 Request started - ID: c0795e8a-5e4d-457c-9305-9f7b0c896f41 | Method: GET | Path: /api/storage/files/4b117dde-04e4-4d6e-a193-0436fa521346/download | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:30:14,082 errors 34416 23036 'WSGIRequest' object has no attribute 'auth'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 129, in download_file
    uploaded_by=request.auth
                ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'auth'
INFO 2025-07-13 13:30:14,112 logging 34416 23036 Request completed - ID: c0795e8a-5e4d-457c-9305-9f7b0c896f41 | Status: 500 | Duration: 0.032s
ERROR 2025-07-13 13:30:14,113 log 34416 23036 Internal Server Error: /api/storage/files/4b117dde-04e4-4d6e-a193-0436fa521346/download
ERROR 2025-07-13 13:30:14,115 basehttp 34416 23036 "GET /api/storage/files/4b117dde-04e4-4d6e-a193-0436fa521346/download HTTP/1.1" 500 446
INFO 2025-07-13 13:30:16,172 logging 34416 30548 Request started - ID: d344737e-4afc-4c94-9ae9-bd534671ec7e | Method: POST | Path: /api/storage/files/4b117dde-04e4-4d6e-a193-0436fa521346/share | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:30:16,176 errors 34416 30548 'WSGIRequest' object has no attribute 'auth'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 172, in create_file_share
    uploaded_by=request.auth
                ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'auth'
INFO 2025-07-13 13:30:16,203 logging 34416 30548 Request completed - ID: d344737e-4afc-4c94-9ae9-bd534671ec7e | Status: 500 | Duration: 0.031s
ERROR 2025-07-13 13:30:16,204 log 34416 30548 Internal Server Error: /api/storage/files/4b117dde-04e4-4d6e-a193-0436fa521346/share
ERROR 2025-07-13 13:30:16,205 basehttp 34416 30548 "POST /api/storage/files/4b117dde-04e4-4d6e-a193-0436fa521346/share HTTP/1.1" 500 450
INFO 2025-07-13 13:30:18,231 logging 34416 24916 Request started - ID: 1bb8ed58-3827-4016-b8a5-91376d9858e7 | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:30:18,233 errors 34416 24916 'WSGIRequest' object has no attribute 'auth'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 73, in list_files
    queryset = StorageFile.objects.filter(uploaded_by=request.auth)
                                                      ^^^^^^^^^^^^
AttributeError: 'WSGIRequest' object has no attribute 'auth'
INFO 2025-07-13 13:30:18,245 logging 34416 24916 Request completed - ID: 1bb8ed58-3827-4016-b8a5-91376d9858e7 | Status: 500 | Duration: 0.015s
ERROR 2025-07-13 13:30:18,245 log 34416 24916 Internal Server Error: /api/storage/files
ERROR 2025-07-13 13:30:18,247 basehttp 34416 24916 "GET /api/storage/files HTTP/1.1" 500 519
INFO 2025-07-13 13:30:20,302 logging 34416 13364 Request started - ID: 0bcaea29-01d5-4495-a5b9-1e514b0c2dbc | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:30:20,320 logging 34416 13364 Request completed - ID: 0bcaea29-01d5-4495-a5b9-1e514b0c2dbc | Status: 401 | Duration: 0.018s
WARNING 2025-07-13 13:30:20,320 log 34416 13364 Unauthorized: /api/storage/stats
WARNING 2025-07-13 13:30:20,321 basehttp 34416 13364 "GET /api/storage/stats HTTP/1.1" 401 37
INFO 2025-07-13 13:30:49,114 autoreload 34416 26076 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:30:50,995 autoreload 1544 36744 Watching for file changes with StatReloader
INFO 2025-07-13 13:31:08,445 autoreload 1544 36744 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:31:10,246 autoreload 9776 37408 Watching for file changes with StatReloader
INFO 2025-07-13 13:31:34,882 autoreload 9776 37408 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:31:36,877 autoreload 29492 15716 Watching for file changes with StatReloader
INFO 2025-07-13 13:31:53,523 autoreload 29492 15716 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:31:55,359 autoreload 1732 36360 Watching for file changes with StatReloader
INFO 2025-07-13 13:32:26,232 logging 1732 34192 Request started - ID: 1e039012-3e05-46eb-b8aa-b20a2ae51c6e | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:32:26,582 logging 1732 34192 Request completed - ID: 1e039012-3e05-46eb-b8aa-b20a2ae51c6e | Status: 500 | Duration: 0.352s
ERROR 2025-07-13 13:32:26,583 log 1732 34192 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:32:26,584 basehttp 1732 34192 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:32:28,640 logging 1732 35300 Request started - ID: 5ff9a2ac-7c88-435a-9094-ef5b8563ad27 | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:32:28,642 errors 1732 35300 Field 'id' expected a number but got <SimpleLazyObject: <django.contrib.auth.models.AnonymousUser object at 0x000002BD4F0ED3A0>>.
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
           ^^^^^^^^^^
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'SimpleLazyObject'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 73, in list_files
    queryset = StorageFile.objects.filter(uploaded_by=request.user)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 1493, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 1511, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 1518, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\query.py", line 1646, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\query.py", line 1678, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\query.py", line 1588, in build_filter
    condition = self.build_lookup(lookups, col, value)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\query.py", line 1415, in build_lookup
    lookup = lookup_class(lhs, rhs)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
               ^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
TypeError: Field 'id' expected a number but got <SimpleLazyObject: <django.contrib.auth.models.AnonymousUser object at 0x000002BD4F0ED3A0>>.
INFO 2025-07-13 13:32:28,679 logging 1732 35300 Request completed - ID: 5ff9a2ac-7c88-435a-9094-ef5b8563ad27 | Status: 500 | Duration: 0.039s
ERROR 2025-07-13 13:32:28,680 log 1732 35300 Internal Server Error: /api/storage/files
ERROR 2025-07-13 13:32:28,680 basehttp 1732 35300 "GET /api/storage/files HTTP/1.1" 500 3305
INFO 2025-07-13 13:32:30,731 logging 1732 37076 Request started - ID: 9b571ba4-60cf-43a1-8ba5-dfe6a8276352 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:32:30,745 logging 1732 37076 Request completed - ID: 9b571ba4-60cf-43a1-8ba5-dfe6a8276352 | Status: 401 | Duration: 0.014s
WARNING 2025-07-13 13:32:30,745 log 1732 37076 Unauthorized: /api/storage/stats
WARNING 2025-07-13 13:32:30,746 basehttp 1732 37076 "GET /api/storage/stats HTTP/1.1" 401 37
INFO 2025-07-13 13:33:15,174 autoreload 1732 36360 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:33:17,161 autoreload 19056 34600 Watching for file changes with StatReloader
INFO 2025-07-13 13:33:34,068 autoreload 19056 34600 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:33:35,854 autoreload 30656 36492 Watching for file changes with StatReloader
INFO 2025-07-13 13:33:53,613 autoreload 30656 36492 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:33:55,399 autoreload 14168 4220 Watching for file changes with StatReloader
INFO 2025-07-13 13:34:35,849 logging 14168 18384 Request started - ID: 077e2c5b-7436-4280-81e9-43be1ce1de05 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:34:36,202 logging 14168 18384 Request completed - ID: 077e2c5b-7436-4280-81e9-43be1ce1de05 | Status: 500 | Duration: 0.352s
ERROR 2025-07-13 13:34:36,203 log 14168 18384 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:34:36,204 basehttp 14168 18384 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:34:38,267 logging 14168 29712 Request started - ID: 47f34d2b-81fe-4f77-8f7b-2161b8854252 | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:34:38,311 errors 14168 29712 1 validation error for StorageFileResponse
download_url
  Field required [type=missing, input_value=<StorageFile: final_test.txt>, input_type=StorageFile]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 94, in list_files
    file_data = StorageFileResponse.from_orm(storage_file)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\pydantic\main.py", line 1440, in from_orm
    return cls.model_validate(obj)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\pydantic\main.py", line 705, in model_validate
    return cls.__pydantic_validator__.validate_python(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pydantic_core._pydantic_core.ValidationError: 1 validation error for StorageFileResponse
download_url
  Field required [type=missing, input_value=<StorageFile: final_test.txt>, input_type=StorageFile]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
INFO 2025-07-13 13:34:38,350 logging 14168 29712 Request completed - ID: 47f34d2b-81fe-4f77-8f7b-2161b8854252 | Status: 500 | Duration: 0.083s
ERROR 2025-07-13 13:34:38,351 log 14168 29712 Internal Server Error: /api/storage/files
ERROR 2025-07-13 13:34:38,352 basehttp 14168 29712 "GET /api/storage/files HTTP/1.1" 500 1122
INFO 2025-07-13 13:34:40,411 logging 14168 34288 Request started - ID: 81ba905c-4186-4239-9aed-88a98f929aed | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:34:40,565 logging 14168 34288 Request completed - ID: 81ba905c-4186-4239-9aed-88a98f929aed | Status: 200 | Duration: 0.153s
INFO 2025-07-13 13:34:40,566 basehttp 14168 34288 "GET /api/storage/stats HTTP/1.1" 200 167
INFO 2025-07-13 13:35:59,455 autoreload 14168 4220 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:36:01,456 autoreload 34952 36624 Watching for file changes with StatReloader
INFO 2025-07-13 13:36:52,764 logging 34952 35512 Request started - ID: 8149c622-7d57-432f-9124-6624ee427fbc | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:36:53,113 logging 34952 35512 Request completed - ID: 8149c622-7d57-432f-9124-6624ee427fbc | Status: 500 | Duration: 0.349s
ERROR 2025-07-13 13:36:53,114 log 34952 35512 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:36:53,116 basehttp 34952 35512 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:36:55,180 logging 34952 21048 Request started - ID: 1d8088e0-4441-4983-8d9f-c7e07b05ac06 | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:36:55,226 logging 34952 21048 Request completed - ID: 1d8088e0-4441-4983-8d9f-c7e07b05ac06 | Status: 200 | Duration: 0.046s
INFO 2025-07-13 13:36:55,227 basehttp 34952 21048 "GET /api/storage/files HTTP/1.1" 200 1695
INFO 2025-07-13 13:36:57,254 logging 34952 25468 Request started - ID: 2771a027-c0ab-45c4-9907-c57ee5ba8988 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:36:57,319 logging 34952 25468 Request completed - ID: 2771a027-c0ab-45c4-9907-c57ee5ba8988 | Status: 200 | Duration: 0.065s
INFO 2025-07-13 13:36:57,320 basehttp 34952 25468 "GET /api/storage/stats HTTP/1.1" 200 167
INFO 2025-07-13 13:37:46,938 autoreload 34952 36624 F:\PycharmProjects\django_ninja_cms\apps\storage\services.py changed, reloading.
INFO 2025-07-13 13:37:48,955 autoreload 29036 20020 Watching for file changes with StatReloader
INFO 2025-07-13 13:39:12,746 logging 29036 34164 Request started - ID: 36e8a733-80d1-47be-96d1-ade5f1702734 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:39:13,051 logging 29036 34164 Request completed - ID: 36e8a733-80d1-47be-96d1-ade5f1702734 | Status: 500 | Duration: 0.306s
ERROR 2025-07-13 13:39:13,052 log 29036 34164 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:39:13,053 basehttp 29036 34164 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:39:15,088 logging 29036 35020 Request started - ID: c5ab61a5-20e2-4329-8f59-78481272c09a | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:39:15,119 logging 29036 35020 Request completed - ID: c5ab61a5-20e2-4329-8f59-78481272c09a | Status: 200 | Duration: 0.031s
INFO 2025-07-13 13:39:15,120 basehttp 29036 35020 "GET /api/storage/files HTTP/1.1" 200 1695
INFO 2025-07-13 13:39:17,175 logging 29036 33936 Request started - ID: 2013e124-c0d3-45df-ae2f-aaafc7bc78ea | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:39:17,242 logging 29036 33936 Request completed - ID: 2013e124-c0d3-45df-ae2f-aaafc7bc78ea | Status: 200 | Duration: 0.067s
INFO 2025-07-13 13:39:17,243 basehttp 29036 33936 "GET /api/storage/stats HTTP/1.1" 200 167
INFO 2025-07-13 13:39:44,695 autoreload 29204 23612 Watching for file changes with StatReloader
INFO 2025-07-13 13:39:54,739 logging 29204 32032 Request started - ID: ba573825-9ac3-4fef-aeb8-5b9de82f2eaa | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:39:55,062 logging 29204 32032 Request completed - ID: ba573825-9ac3-4fef-aeb8-5b9de82f2eaa | Status: 500 | Duration: 0.322s
ERROR 2025-07-13 13:39:55,064 log 29204 32032 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:39:55,064 basehttp 29204 32032 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:39:57,096 logging 29204 29492 Request started - ID: 378eab30-4310-4baa-8bbe-527f0309515f | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:39:57,130 logging 29204 29492 Request completed - ID: 378eab30-4310-4baa-8bbe-527f0309515f | Status: 200 | Duration: 0.035s
INFO 2025-07-13 13:39:57,131 basehttp 29204 29492 "GET /api/storage/files HTTP/1.1" 200 1695
INFO 2025-07-13 13:39:59,162 logging 29204 11600 Request started - ID: 462068dc-b327-4b99-9cba-3fb9a13b04dc | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:39:59,274 logging 29204 11600 Request completed - ID: 462068dc-b327-4b99-9cba-3fb9a13b04dc | Status: 200 | Duration: 0.113s
INFO 2025-07-13 13:39:59,277 basehttp 29204 11600 "GET /api/storage/stats HTTP/1.1" 200 167
INFO 2025-07-13 13:41:18,081 autoreload 29204 23612 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:41:20,160 autoreload 9404 7080 Watching for file changes with StatReloader
INFO 2025-07-13 13:42:07,599 logging 9404 33096 Request started - ID: ea94fe62-303f-46eb-ae75-85d4f5380d2b | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:42:07,926 logging 9404 33096 Request completed - ID: ea94fe62-303f-46eb-ae75-85d4f5380d2b | Status: 500 | Duration: 0.327s
ERROR 2025-07-13 13:42:07,928 log 9404 33096 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:42:07,929 basehttp 9404 33096 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:42:09,970 logging 9404 37096 Request started - ID: 9167f1c2-10bb-491c-8dab-e921660f0f9c | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:42:10,028 logging 9404 37096 Request completed - ID: 9167f1c2-10bb-491c-8dab-e921660f0f9c | Status: 200 | Duration: 0.058s
INFO 2025-07-13 13:42:10,029 basehttp 9404 37096 "GET /api/storage/files HTTP/1.1" 200 1695
INFO 2025-07-13 13:42:12,070 logging 9404 37088 Request started - ID: de2a5a99-1535-4a67-81a3-9cc1930e1541 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:42:12,158 logging 9404 37088 Request completed - ID: de2a5a99-1535-4a67-81a3-9cc1930e1541 | Status: 200 | Duration: 0.088s
INFO 2025-07-13 13:42:12,160 basehttp 9404 37088 "GET /api/storage/stats HTTP/1.1" 200 167
INFO 2025-07-13 13:42:57,614 logging 9404 29628 Request started - ID: 72817caa-6c4d-4b46-8250-ed96880677a7 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:42:57,674 logging 9404 29628 Request completed - ID: 72817caa-6c4d-4b46-8250-ed96880677a7 | Status: 200 | Duration: 0.060s
INFO 2025-07-13 13:42:57,675 basehttp 9404 29628 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:42:58,842 logging 9404 29628 Request started - ID: d6fc75c1-6d55-4026-8f18-47e23e8d409b | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:42:58,956 logging 9404 29628 Request completed - ID: d6fc75c1-6d55-4026-8f18-47e23e8d409b | Status: 200 | Duration: 0.114s
INFO 2025-07-13 13:42:58,958 basehttp 9404 29628 "GET /api/api/openapi.json HTTP/1.1" 200 40103
INFO 2025-07-13 13:43:10,766 logging 9404 29628 Request started - ID: 6be3607e-f7f4-4d5b-9d48-f8e7123c2c0f | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:43:10,794 logging 9404 29628 Request completed - ID: 6be3607e-f7f4-4d5b-9d48-f8e7123c2c0f | Status: 401 | Duration: 0.028s
WARNING 2025-07-13 13:43:10,794 log 9404 29628 Unauthorized: /api/storage/upload
WARNING 2025-07-13 13:43:10,795 basehttp 9404 29628 "POST /api/storage/upload HTTP/1.1" 401 26
INFO 2025-07-13 13:43:33,317 logging 9404 29628 Request started - ID: 0a91138f-9394-4ecd-9d64-fcf95d242bf3 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:43:33,358 logging 9404 29628 Request completed - ID: 0a91138f-9394-4ecd-9d64-fcf95d242bf3 | Status: 500 | Duration: 0.041s
ERROR 2025-07-13 13:43:33,358 log 9404 29628 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:43:33,360 basehttp 9404 29628 "POST /api/storage/upload HTTP/1.1" 500 32
INFO 2025-07-13 13:44:31,410 autoreload 9404 7080 F:\PycharmProjects\django_ninja_cms\apps\storage\api.py changed, reloading.
INFO 2025-07-13 13:44:33,474 autoreload 36152 37420 Watching for file changes with StatReloader
INFO 2025-07-13 13:45:12,413 autoreload 34588 34976 Watching for file changes with StatReloader
INFO 2025-07-13 13:45:20,457 logging 34588 20980 Request started - ID: 2ce3ba46-2b1c-48c6-9d0a-c783c6c76d6b | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:45:20,718 logging 34588 20980 Request completed - ID: 2ce3ba46-2b1c-48c6-9d0a-c783c6c76d6b | Status: 401 | Duration: 0.261s
WARNING 2025-07-13 13:45:20,722 log 34588 20980 Unauthorized: /api/storage/upload
WARNING 2025-07-13 13:45:20,724 basehttp 34588 20980 "POST /api/storage/upload HTTP/1.1" 401 427
INFO 2025-07-13 13:45:26,114 logging 34588 20980 Request started - ID: d35c68b3-0de1-44fb-a50c-38cca123be59 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:45:26,202 logging 34588 20980 Request completed - ID: d35c68b3-0de1-44fb-a50c-38cca123be59 | Status: 200 | Duration: 0.087s
INFO 2025-07-13 13:45:26,205 basehttp 34588 20980 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:45:27,319 logging 34588 20980 Request started - ID: 428fb43d-f3f7-4c77-ad8f-f019bfb01e2c | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:45:27,419 logging 34588 20980 Request completed - ID: 428fb43d-f3f7-4c77-ad8f-f019bfb01e2c | Status: 200 | Duration: 0.100s
INFO 2025-07-13 13:45:27,420 basehttp 34588 20980 "GET /api/api/openapi.json HTTP/1.1" 200 40103
INFO 2025-07-13 13:45:50,464 logging 34588 20980 Request started - ID: 6ed3ea2b-1cd5-4d4b-bcf0-e93f8f06b801 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:45:50,510 logging 34588 20980 Request completed - ID: 6ed3ea2b-1cd5-4d4b-bcf0-e93f8f06b801 | Status: 401 | Duration: 0.046s
WARNING 2025-07-13 13:45:50,510 log 34588 20980 Unauthorized: /api/storage/upload
WARNING 2025-07-13 13:45:50,511 basehttp 34588 20980 "POST /api/storage/upload HTTP/1.1" 401 427
INFO 2025-07-13 13:46:14,200 logging 34588 17552 Request started - ID: 5ba83253-e31e-47ad-89e7-702f86654c88 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:14,251 logging 34588 17552 Request completed - ID: 5ba83253-e31e-47ad-89e7-702f86654c88 | Status: 422 | Duration: 0.052s
WARNING 2025-07-13 13:46:14,252 log 34588 17552 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:46:14,253 basehttp 34588 17552 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:46:16,296 logging 34588 30144 Request started - ID: bb983867-89cd-4e2b-a648-0a1d0bc35640 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:17,726 logging 34588 30144 Request completed - ID: bb983867-89cd-4e2b-a648-0a1d0bc35640 | Status: 200 | Duration: 1.429s
INFO 2025-07-13 13:46:17,726 basehttp 34588 30144 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:46:19,770 logging 34588 17860 Request started - ID: ff274b1d-bcd9-495b-b24c-60fb78b8f4fa | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:19,908 logging 34588 17860 Request completed - ID: ff274b1d-bcd9-495b-b24c-60fb78b8f4fa | Status: 500 | Duration: 0.138s
ERROR 2025-07-13 13:46:19,909 log 34588 17860 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:46:19,911 basehttp 34588 17860 "POST /api/storage/upload HTTP/1.1" 500 89
INFO 2025-07-13 13:46:19,916 logging 34588 17860 Request started - ID: 5b1e45e9-2f73-40e8-a201-000dd67c24ae | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:19,960 logging 34588 17860 Request completed - ID: 5b1e45e9-2f73-40e8-a201-000dd67c24ae | Status: 500 | Duration: 0.044s
ERROR 2025-07-13 13:46:19,960 log 34588 17860 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:46:19,961 basehttp 34588 17860 "POST /api/storage/upload HTTP/1.1" 500 89
INFO 2025-07-13 13:46:21,998 logging 34588 21804 Request started - ID: 45ea3c11-c994-4e00-aba8-6fd00e354d1c | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:22,016 logging 34588 21804 Request completed - ID: 45ea3c11-c994-4e00-aba8-6fd00e354d1c | Status: 200 | Duration: 0.018s
INFO 2025-07-13 13:46:22,017 basehttp 34588 21804 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:46:30,388 logging 34588 20980 Request started - ID: 7b1593b8-1f9d-4d3b-ad0c-86287fae0748 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:30,421 logging 34588 20980 Request completed - ID: 7b1593b8-1f9d-4d3b-ad0c-86287fae0748 | Status: 200 | Duration: 0.033s
INFO 2025-07-13 13:46:30,422 basehttp 34588 20980 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:46:31,465 logging 34588 20980 Request started - ID: 42a77272-a0b9-4331-a4e9-0cb3b31b0b1c | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:31,542 logging 34588 20980 Request completed - ID: 42a77272-a0b9-4331-a4e9-0cb3b31b0b1c | Status: 200 | Duration: 0.077s
INFO 2025-07-13 13:46:31,543 basehttp 34588 20980 "GET /api/api/openapi.json HTTP/1.1" 200 40103
INFO 2025-07-13 13:46:40,787 logging 34588 20980 Request started - ID: 218ceab9-ff28-48d9-aec5-08956ceb7941 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:46:40,833 logging 34588 20980 Request completed - ID: 218ceab9-ff28-48d9-aec5-08956ceb7941 | Status: 401 | Duration: 0.046s
WARNING 2025-07-13 13:46:40,833 log 34588 20980 Unauthorized: /api/storage/upload
WARNING 2025-07-13 13:46:40,834 basehttp 34588 20980 "POST /api/storage/upload HTTP/1.1" 401 26
INFO 2025-07-13 13:48:26,209 logging 34588 37656 Request started - ID: 371bf427-122b-4296-a40d-5ce374074469 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:48:26,266 logging 34588 37656 Request completed - ID: 371bf427-122b-4296-a40d-5ce374074469 | Status: 422 | Duration: 0.057s
WARNING 2025-07-13 13:48:26,267 log 34588 37656 Unprocessable Entity: /api/auth/login
WARNING 2025-07-13 13:48:26,270 basehttp 34588 37656 "POST /api/auth/login HTTP/1.1" 422 92
INFO 2025-07-13 13:48:37,899 logging 34588 35788 Request started - ID: acd0bc7d-724f-40dc-b76a-238cf6714488 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:48:37,917 logging 34588 35788 Request completed - ID: acd0bc7d-724f-40dc-b76a-238cf6714488 | Status: 422 | Duration: 0.018s
WARNING 2025-07-13 13:48:37,918 log 34588 35788 Unprocessable Entity: /api/auth/login
WARNING 2025-07-13 13:48:37,919 basehttp 34588 35788 "POST /api/auth/login HTTP/1.1" 422 92
INFO 2025-07-13 13:48:55,443 logging 34588 4888 Request started - ID: 1d07e3a8-23f1-440d-ba30-e979a6c7f9ef | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:48:55,489 logging 34588 4888 Request completed - ID: 1d07e3a8-23f1-440d-ba30-e979a6c7f9ef | Status: 422 | Duration: 0.045s
WARNING 2025-07-13 13:48:55,489 log 34588 4888 Unprocessable Entity: /api/auth/login
WARNING 2025-07-13 13:48:55,491 basehttp 34588 4888 "POST /api/auth/login HTTP/1.1" 422 92
INFO 2025-07-13 13:49:08,553 logging 34588 37052 Request started - ID: aeda27e8-02c6-463a-8542-f196a84b1ef2 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:08,597 logging 34588 37052 Request completed - ID: aeda27e8-02c6-463a-8542-f196a84b1ef2 | Status: 422 | Duration: 0.045s
WARNING 2025-07-13 13:49:08,598 log 34588 37052 Unprocessable Entity: /api/auth/login
WARNING 2025-07-13 13:49:08,599 basehttp 34588 37052 "POST /api/auth/login HTTP/1.1" 422 92
INFO 2025-07-13 13:49:26,846 logging 34588 7424 Request started - ID: 1c63c0ee-bf14-4a2d-be6e-a74d27e34737 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:26,887 logging 34588 7424 Request completed - ID: 1c63c0ee-bf14-4a2d-be6e-a74d27e34737 | Status: 422 | Duration: 0.042s
WARNING 2025-07-13 13:49:26,889 log 34588 7424 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:49:26,892 basehttp 34588 7424 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:49:28,938 logging 34588 8100 Request started - ID: 3cca1baf-053b-4c9c-8116-c4536433c8b7 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:30,109 logging 34588 8100 Request completed - ID: 3cca1baf-053b-4c9c-8116-c4536433c8b7 | Status: 200 | Duration: 1.171s
INFO 2025-07-13 13:49:30,111 basehttp 34588 8100 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:49:32,153 logging 34588 34392 Request started - ID: aa8fe603-2f1f-43e9-9378-4dd8e614ff6e | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:32,219 logging 34588 34392 Request completed - ID: aa8fe603-2f1f-43e9-9378-4dd8e614ff6e | Status: 500 | Duration: 0.066s
ERROR 2025-07-13 13:49:32,219 log 34588 34392 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:49:32,220 basehttp 34588 34392 "POST /api/storage/upload HTTP/1.1" 500 89
INFO 2025-07-13 13:49:32,225 logging 34588 34392 Request started - ID: fb26045b-88c1-4484-866a-c978de8f99ec | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:32,296 logging 34588 34392 Request completed - ID: fb26045b-88c1-4484-866a-c978de8f99ec | Status: 500 | Duration: 0.070s
ERROR 2025-07-13 13:49:32,296 log 34588 34392 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:49:32,297 basehttp 34588 34392 "POST /api/storage/upload HTTP/1.1" 500 89
INFO 2025-07-13 13:49:34,321 logging 34588 32520 Request started - ID: 232f341d-69ea-40e8-8c1d-bb6386631683 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:34,335 logging 34588 32520 Request completed - ID: 232f341d-69ea-40e8-8c1d-bb6386631683 | Status: 200 | Duration: 0.014s
INFO 2025-07-13 13:49:34,336 basehttp 34588 32520 "GET /api/docs/ HTTP/1.1" 200 13773
INFO 2025-07-13 13:49:42,541 logging 34588 7928 Request started - ID: 0843eb05-a2fe-45ce-ab3e-05804750586d | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:42,625 logging 34588 7928 Request completed - ID: 0843eb05-a2fe-45ce-ab3e-05804750586d | Status: 200 | Duration: 0.083s
INFO 2025-07-13 13:49:42,629 basehttp 34588 7928 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:49:43,752 logging 34588 7928 Request started - ID: c29bd7a5-e693-48a4-9916-8a1b7909466e | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:49:43,841 logging 34588 7928 Request completed - ID: c29bd7a5-e693-48a4-9916-8a1b7909466e | Status: 200 | Duration: 0.089s
INFO 2025-07-13 13:49:43,842 basehttp 34588 7928 "GET /api/api/openapi.json HTTP/1.1" 200 40103
INFO 2025-07-13 13:50:06,363 logging 34588 7928 Request started - ID: af92798a-0adb-4f98-975e-102298dcae3f | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:50:06,400 logging 34588 7928 Request completed - ID: af92798a-0adb-4f98-975e-102298dcae3f | Status: 500 | Duration: 0.037s
ERROR 2025-07-13 13:50:06,400 log 34588 7928 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:50:06,402 basehttp 34588 7928 "POST /api/storage/upload HTTP/1.1" 500 89
INFO 2025-07-13 13:50:36,868 autoreload 34588 34976 F:\PycharmProjects\django_ninja_cms\apps\storage\models.py changed, reloading.
INFO 2025-07-13 13:50:38,919 autoreload 23564 37748 Watching for file changes with StatReloader
INFO 2025-07-13 13:51:07,665 logging 23564 31032 Request started - ID: 206a2de8-fa09-4f3c-9d01-26c26c40ca13 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:51:07,991 logging 23564 31032 Request completed - ID: 206a2de8-fa09-4f3c-9d01-26c26c40ca13 | Status: 500 | Duration: 0.325s
ERROR 2025-07-13 13:51:07,994 log 23564 31032 Internal Server Error: /api/storage/upload
ERROR 2025-07-13 13:51:07,995 basehttp 23564 31032 "POST /api/storage/upload HTTP/1.1" 500 89
INFO 2025-07-13 13:52:02,210 autoreload 35656 36972 Watching for file changes with StatReloader
INFO 2025-07-13 13:52:02,817 logging 35656 19356 Request started - ID: 216c938c-aac8-47ba-9a3d-a87b0aaf06fd | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:02,849 logging 35656 19356 Request completed - ID: 216c938c-aac8-47ba-9a3d-a87b0aaf06fd | Status: 200 | Duration: 0.033s
INFO 2025-07-13 13:52:02,851 basehttp 35656 19356 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:52:03,976 logging 35656 19356 Request started - ID: 5ba2c414-46f4-4dcc-9536-db780385326d | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:04,101 logging 35656 19356 Request completed - ID: 5ba2c414-46f4-4dcc-9536-db780385326d | Status: 200 | Duration: 0.125s
INFO 2025-07-13 13:52:04,102 basehttp 35656 19356 "GET /api/api/openapi.json HTTP/1.1" 200 40103
INFO 2025-07-13 13:52:20,461 logging 35656 20024 Request started - ID: 7899f5d2-498d-44e2-939d-86a63acf341f | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:20,510 logging 35656 20024 Request completed - ID: 7899f5d2-498d-44e2-939d-86a63acf341f | Status: 422 | Duration: 0.049s
WARNING 2025-07-13 13:52:20,511 log 35656 20024 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:52:20,512 basehttp 35656 20024 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:52:22,555 logging 35656 18616 Request started - ID: f6eaa187-a108-4029-9155-371b3e198d13 | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:23,767 logging 35656 18616 Request completed - ID: f6eaa187-a108-4029-9155-371b3e198d13 | Status: 200 | Duration: 1.213s
INFO 2025-07-13 13:52:23,768 basehttp 35656 18616 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:52:25,811 logging 35656 6300 Request started - ID: 0d176ea5-04d3-46ce-8bea-6e00420bcf2f | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:25,876 logging 35656 6300 Request completed - ID: 0d176ea5-04d3-46ce-8bea-6e00420bcf2f | Status: 200 | Duration: 0.065s
INFO 2025-07-13 13:52:25,876 basehttp 35656 6300 "POST /api/storage/upload HTTP/1.1" 200 419
INFO 2025-07-13 13:52:25,880 logging 35656 6300 Request started - ID: 2f66535a-8b39-4ce2-b415-69520823d590 | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:25,910 logging 35656 6300 Request completed - ID: 2f66535a-8b39-4ce2-b415-69520823d590 | Status: 200 | Duration: 0.030s
INFO 2025-07-13 13:52:25,911 basehttp 35656 6300 "GET /api/storage/files HTTP/1.1" 200 2100
INFO 2025-07-13 13:52:25,914 logging 35656 6300 Request started - ID: 79440aab-4e35-4144-a197-63ccea158323 | Method: GET | Path: /api/storage/files/680e293d-1a7f-42df-959c-333fc5444ef5/download | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:25,978 logging 35656 6300 Request completed - ID: 79440aab-4e35-4144-a197-63ccea158323 | Status: 200 | Duration: 0.065s
INFO 2025-07-13 13:52:25,999 basehttp 35656 6300 "GET /api/storage/files/680e293d-1a7f-42df-959c-333fc5444ef5/download HTTP/1.1" 200 43
INFO 2025-07-13 13:52:26,003 logging 35656 6300 Request started - ID: 891ff22c-ada1-44db-a031-8e3d14f14cdd | Method: DELETE | Path: /api/storage/files/680e293d-1a7f-42df-959c-333fc5444ef5 | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:26,051 logging 35656 6300 Request completed - ID: 891ff22c-ada1-44db-a031-8e3d14f14cdd | Status: 200 | Duration: 0.048s
INFO 2025-07-13 13:52:26,052 basehttp 35656 6300 "DELETE /api/storage/files/680e293d-1a7f-42df-959c-333fc5444ef5 HTTP/1.1" 200 40
INFO 2025-07-13 13:52:26,055 logging 35656 6300 Request started - ID: 25a54d17-a072-4b4b-b7a2-ea8e3d3e512f | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:26,084 logging 35656 6300 Request completed - ID: 25a54d17-a072-4b4b-b7a2-ea8e3d3e512f | Status: 200 | Duration: 0.031s
INFO 2025-07-13 13:52:26,085 basehttp 35656 6300 "POST /api/storage/upload HTTP/1.1" 200 425
INFO 2025-07-13 13:52:26,088 logging 35656 6300 Request started - ID: 3e6e8bfd-4257-4166-b15e-0a92b5902be2 | Method: POST | Path: /api/storage/files/1c294c0e-0b2e-4c97-9209-afedb455813e/share | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:52:26,095 errors 35656 6300 ['“0CYbcopj9jE7ErRMFPcpO4zkhHxMnnCNhosKd9XBYWI” is not a valid UUID.']
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2766, in to_python
    return uuid.UUID(**{input_form: value})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\uuid.py", line 178, in __init__
    raise ValueError('badly formed hexadecimal UUID string')
ValueError: badly formed hexadecimal UUID string

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 203, in create_file_share
    file_share = StorageService.create_file_share(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\services.py", line 110, in create_file_share
    file_share.save()
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1881, in execute_sql
    for sql, params in self.as_sql():
                       ^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1806, in as_sql
    self.prepare_value(field, self.pre_save_val(field, obj))
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1741, in prepare_value
    return field.get_db_prep_save(value, connection=self.connection)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 1012, in get_db_prep_save
    return self.get_db_prep_value(value, connection=connection, prepared=False)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2756, in get_db_prep_value
    value = self.to_python(value)
            ^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\django\db\models\fields\__init__.py", line 2768, in to_python
    raise exceptions.ValidationError(
django.core.exceptions.ValidationError: ['“0CYbcopj9jE7ErRMFPcpO4zkhHxMnnCNhosKd9XBYWI” is not a valid UUID.']
INFO 2025-07-13 13:52:26,122 logging 35656 6300 Request completed - ID: 3e6e8bfd-4257-4166-b15e-0a92b5902be2 | Status: 500 | Duration: 0.034s
ERROR 2025-07-13 13:52:26,122 log 35656 6300 Internal Server Error: /api/storage/files/1c294c0e-0b2e-4c97-9209-afedb455813e/share
ERROR 2025-07-13 13:52:26,123 basehttp 35656 6300 "POST /api/storage/files/1c294c0e-0b2e-4c97-9209-afedb455813e/share HTTP/1.1" 500 3713
INFO 2025-07-13 13:52:28,168 logging 35656 24848 Request started - ID: ad797c04-4c08-4594-a0f2-e95deedf66f4 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:52:28,216 logging 35656 24848 Request completed - ID: ad797c04-4c08-4594-a0f2-e95deedf66f4 | Status: 200 | Duration: 0.049s
INFO 2025-07-13 13:52:28,218 basehttp 35656 24848 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:53:07,097 autoreload 35656 36972 F:\PycharmProjects\django_ninja_cms\apps\storage\services.py changed, reloading.
INFO 2025-07-13 13:53:09,054 autoreload 33872 2788 Watching for file changes with StatReloader
INFO 2025-07-13 13:53:39,856 logging 33872 36088 Request started - ID: fd109794-7472-416f-8f5d-2f0c1e011fc5 | Method: POST | Path: /api/auth/register | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:39,933 logging 33872 36088 Request completed - ID: fd109794-7472-416f-8f5d-2f0c1e011fc5 | Status: 422 | Duration: 0.077s
WARNING 2025-07-13 13:53:39,935 log 33872 36088 Unprocessable Entity: /api/auth/register
WARNING 2025-07-13 13:53:39,936 basehttp 33872 36088 "POST /api/auth/register HTTP/1.1" 422 181
INFO 2025-07-13 13:53:42,002 logging 33872 12436 Request started - ID: 911a8f9e-d89c-405f-a543-635f2e41439d | Method: POST | Path: /api/auth/login | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:43,176 logging 33872 12436 Request completed - ID: 911a8f9e-d89c-405f-a543-635f2e41439d | Status: 200 | Duration: 1.174s
INFO 2025-07-13 13:53:43,177 basehttp 33872 12436 "POST /api/auth/login HTTP/1.1" 200 709
INFO 2025-07-13 13:53:45,206 logging 33872 6756 Request started - ID: 0fbedd0a-dcb7-44bb-bcab-7522acf1d5c2 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:45,274 logging 33872 6756 Request completed - ID: 0fbedd0a-dcb7-44bb-bcab-7522acf1d5c2 | Status: 200 | Duration: 0.069s
INFO 2025-07-13 13:53:45,275 basehttp 33872 6756 "POST /api/storage/upload HTTP/1.1" 200 419
INFO 2025-07-13 13:53:45,277 logging 33872 6756 Request started - ID: 9c6bde97-ddc6-4c3e-b82c-3ce2df6567f5 | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:45,306 logging 33872 6756 Request completed - ID: 9c6bde97-ddc6-4c3e-b82c-3ce2df6567f5 | Status: 200 | Duration: 0.029s
INFO 2025-07-13 13:53:45,307 basehttp 33872 6756 "GET /api/storage/files HTTP/1.1" 200 2511
INFO 2025-07-13 13:53:45,310 logging 33872 6756 Request started - ID: 1094638a-c528-4353-a1ee-cfb4beec1b95 | Method: GET | Path: /api/storage/files/88814dea-9a2e-4203-b2ee-2ec58acdb8c1/download | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:45,349 logging 33872 6756 Request completed - ID: 1094638a-c528-4353-a1ee-cfb4beec1b95 | Status: 200 | Duration: 0.039s
INFO 2025-07-13 13:53:45,350 basehttp 33872 6756 "GET /api/storage/files/88814dea-9a2e-4203-b2ee-2ec58acdb8c1/download HTTP/1.1" 200 43
INFO 2025-07-13 13:53:45,352 logging 33872 6756 Request started - ID: ea9dce19-62db-4899-ae9a-9270b387f217 | Method: DELETE | Path: /api/storage/files/88814dea-9a2e-4203-b2ee-2ec58acdb8c1 | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:45,388 logging 33872 6756 Request completed - ID: ea9dce19-62db-4899-ae9a-9270b387f217 | Status: 200 | Duration: 0.036s
INFO 2025-07-13 13:53:45,389 basehttp 33872 6756 "DELETE /api/storage/files/88814dea-9a2e-4203-b2ee-2ec58acdb8c1 HTTP/1.1" 200 40
INFO 2025-07-13 13:53:45,392 logging 33872 6756 Request started - ID: 6b92eaa5-5572-4299-ae22-51306edcd5b8 | Method: POST | Path: /api/storage/upload | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:45,425 logging 33872 6756 Request completed - ID: 6b92eaa5-5572-4299-ae22-51306edcd5b8 | Status: 200 | Duration: 0.033s
INFO 2025-07-13 13:53:45,426 basehttp 33872 6756 "POST /api/storage/upload HTTP/1.1" 200 425
INFO 2025-07-13 13:53:45,428 logging 33872 6756 Request started - ID: dfe87738-fbb1-4a4a-8fc6-207a0d035468 | Method: POST | Path: /api/storage/files/2cb0eac3-b957-467c-843e-82001cbda210/share | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:45,462 logging 33872 6756 Request completed - ID: dfe87738-fbb1-4a4a-8fc6-207a0d035468 | Status: 200 | Duration: 0.034s
INFO 2025-07-13 13:53:45,462 basehttp 33872 6756 "POST /api/storage/files/2cb0eac3-b957-467c-843e-82001cbda210/share HTTP/1.1" 200 251
INFO 2025-07-13 13:53:47,515 logging 33872 34064 Request started - ID: ab5efd84-6546-4a93-bb57-18bbbbfa0a36 | Method: GET | Path: /api/storage/share/291fd9b8-a64f-4331-82c6-e0273709df50 | User:  | IP: 127.0.0.1
ERROR 2025-07-13 13:53:47,534 errors 33872 34064 'FileShare' object has no attribute 'can_access'
Traceback (most recent call last):
  File "F:\PycharmProjects\django_ninja_cms\venv\Lib\site-packages\ninja\operation.py", line 133, in run
    result = self.view_func(request, **values)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\PycharmProjects\django_ninja_cms\apps\storage\api.py", line 257, in access_shared_file
    if not file_share.can_access(user=getattr(request, 'auth', None), password=password):
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'FileShare' object has no attribute 'can_access'
INFO 2025-07-13 13:53:47,611 logging 33872 34064 Request completed - ID: ab5efd84-6546-4a93-bb57-18bbbbfa0a36 | Status: 500 | Duration: 0.096s
ERROR 2025-07-13 13:53:47,612 log 33872 34064 Internal Server Error: /api/storage/share/291fd9b8-a64f-4331-82c6-e0273709df50
ERROR 2025-07-13 13:53:47,613 basehttp 33872 34064 "GET /api/storage/share/291fd9b8-a64f-4331-82c6-e0273709df50 HTTP/1.1" 500 520
INFO 2025-07-13 13:53:47,617 logging 33872 6756 Request started - ID: 2271d956-78c1-447c-b12c-5638acf1f595 | Method: GET | Path: /api/storage/stats | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:47,662 logging 33872 6756 Request completed - ID: 2271d956-78c1-447c-b12c-5638acf1f595 | Status: 200 | Duration: 0.045s
INFO 2025-07-13 13:53:47,663 basehttp 33872 6756 "GET /api/storage/stats HTTP/1.1" 200 168
INFO 2025-07-13 13:53:49,715 logging 33872 28720 Request started - ID: 88aa5165-2263-434c-b5d4-507c459678e9 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:53:49,778 logging 33872 28720 Request completed - ID: 88aa5165-2263-434c-b5d4-507c459678e9 | Status: 200 | Duration: 0.064s
INFO 2025-07-13 13:53:49,778 basehttp 33872 28720 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 13:54:38,989 logging 33872 37328 Request started - ID: e0c20327-6e84-430c-9725-506f9e8362dc | Method: GET | Path: /api/storage/files | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:54:39,046 logging 33872 37328 Request completed - ID: e0c20327-6e84-430c-9725-506f9e8362dc | Status: 200 | Duration: 0.057s
INFO 2025-07-13 13:54:39,049 basehttp 33872 37328 "GET /api/storage/files?page=1&per_page=20 HTTP/1.1" 200 2517
INFO 2025-07-13 13:55:17,824 logging 33872 37328 Request started - ID: 92b4f964-d840-422b-910c-8bbc93c4241e | Method: GET | Path: /api/storage/files/2cb0eac3-b957-467c-843e-82001cbda210 | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:55:17,859 logging 33872 37328 Request completed - ID: 92b4f964-d840-422b-910c-8bbc93c4241e | Status: 200 | Duration: 0.035s
INFO 2025-07-13 13:55:17,863 basehttp 33872 37328 "GET /api/storage/files/2cb0eac3-b957-467c-843e-82001cbda210 HTTP/1.1" 200 283
INFO 2025-07-13 13:55:24,628 logging 33872 37328 Request started - ID: 272a964c-754b-4ffe-9641-094017bc11b5 | Method: DELETE | Path: /api/storage/files/2cb0eac3-b957-467c-843e-82001cbda210 | User:  | IP: 127.0.0.1
INFO 2025-07-13 13:55:24,666 logging 33872 37328 Request completed - ID: 272a964c-754b-4ffe-9641-094017bc11b5 | Status: 200 | Duration: 0.038s
INFO 2025-07-13 13:55:24,668 basehttp 33872 37328 "DELETE /api/storage/files/2cb0eac3-b957-467c-843e-82001cbda210 HTTP/1.1" 200 40
INFO 2025-07-13 14:11:33,037 autoreload 33872 2788 F:\PycharmProjects\django_ninja_cms\apps\storage\models.py changed, reloading.
INFO 2025-07-13 14:11:35,353 autoreload 37080 37792 Watching for file changes with StatReloader
INFO 2025-07-13 14:12:07,450 autoreload 37080 37792 F:\PycharmProjects\django_ninja_cms\apps\storage\services.py changed, reloading.
INFO 2025-07-13 14:12:09,682 autoreload 24268 37408 Watching for file changes with StatReloader
INFO 2025-07-13 19:15:04,515 auto_permissions 7744 34296 创建权限: 查看 文章
INFO 2025-07-13 19:15:04,524 auto_permissions 7744 34296 创建权限: 创建 文章
INFO 2025-07-13 19:15:04,532 auto_permissions 7744 34296 创建权限: 编辑 文章
INFO 2025-07-13 19:15:04,539 auto_permissions 7744 34296 创建权限: 删除 文章
INFO 2025-07-13 19:15:04,547 auto_permissions 7744 34296 创建权限: 管理 文章
INFO 2025-07-13 19:15:04,555 auto_permissions 7744 34296 创建权限: 查看 分类
INFO 2025-07-13 19:15:04,563 auto_permissions 7744 34296 创建权限: 创建 分类
INFO 2025-07-13 19:15:04,571 auto_permissions 7744 34296 创建权限: 编辑 分类
INFO 2025-07-13 19:15:04,578 auto_permissions 7744 34296 创建权限: 删除 分类
INFO 2025-07-13 19:15:04,587 auto_permissions 7744 34296 创建权限: 管理 分类
INFO 2025-07-13 19:15:04,595 auto_permissions 7744 34296 创建权限: 查看 标签
INFO 2025-07-13 19:15:04,602 auto_permissions 7744 34296 创建权限: 创建 标签
INFO 2025-07-13 19:15:04,610 auto_permissions 7744 34296 创建权限: 编辑 标签
INFO 2025-07-13 19:15:04,618 auto_permissions 7744 34296 创建权限: 删除 标签
INFO 2025-07-13 19:15:04,625 auto_permissions 7744 34296 创建权限: 管理 标签
INFO 2025-07-13 19:15:04,632 auto_permissions 7744 34296 创建权限: 查看 评论
INFO 2025-07-13 19:15:04,639 auto_permissions 7744 34296 创建权限: 创建 评论
INFO 2025-07-13 19:15:04,648 auto_permissions 7744 34296 创建权限: 编辑 评论
INFO 2025-07-13 19:15:04,690 auto_permissions 7744 34296 创建权限: 删除 评论
INFO 2025-07-13 19:15:04,711 auto_permissions 7744 34296 创建权限: 管理 评论
INFO 2025-07-13 19:15:04,720 auto_permissions 7744 34296 创建权限: 查看 文章浏览记录
INFO 2025-07-13 19:15:04,733 auto_permissions 7744 34296 创建权限: 创建 文章浏览记录
INFO 2025-07-13 19:15:04,742 auto_permissions 7744 34296 创建权限: 编辑 文章浏览记录
INFO 2025-07-13 19:15:04,751 auto_permissions 7744 34296 创建权限: 删除 文章浏览记录
INFO 2025-07-13 19:15:04,760 auto_permissions 7744 34296 创建权限: 管理 文章浏览记录
INFO 2025-07-13 19:15:04,768 auto_permissions 7744 34296 创建权限: 查看 文章点赞
INFO 2025-07-13 19:15:04,777 auto_permissions 7744 34296 创建权限: 创建 文章点赞
INFO 2025-07-13 19:15:04,786 auto_permissions 7744 34296 创建权限: 编辑 文章点赞
INFO 2025-07-13 19:15:04,794 auto_permissions 7744 34296 创建权限: 删除 文章点赞
INFO 2025-07-13 19:15:04,801 auto_permissions 7744 34296 创建权限: 管理 文章点赞
INFO 2025-07-13 19:15:04,809 auto_permissions 7744 34296 创建权限: 查看 邮件订阅
INFO 2025-07-13 19:15:04,816 auto_permissions 7744 34296 创建权限: 创建 邮件订阅
INFO 2025-07-13 19:15:04,823 auto_permissions 7744 34296 创建权限: 编辑 邮件订阅
INFO 2025-07-13 19:15:04,831 auto_permissions 7744 34296 创建权限: 删除 邮件订阅
INFO 2025-07-13 19:15:04,839 auto_permissions 7744 34296 创建权限: 管理 邮件订阅
INFO 2025-07-13 19:15:04,846 auto_permissions 7744 34296 创建角色: Cms 查看者
INFO 2025-07-13 19:15:04,901 auto_permissions 7744 34296 创建角色: Cms 编辑者
INFO 2025-07-13 19:15:05,049 auto_permissions 7744 34296 创建角色: Cms 管理员
INFO 2025-07-13 19:17:35,998 autoreload 21836 27328 Watching for file changes with StatReloader
INFO 2025-07-13 19:18:29,773 autoreload 18104 22852 Watching for file changes with StatReloader
INFO 2025-07-13 19:19:21,718 autoreload 18104 22852 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:19:23,564 autoreload 12932 21276 Watching for file changes with StatReloader
INFO 2025-07-13 19:19:39,917 autoreload 12932 21276 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:19:41,729 autoreload 19380 33904 Watching for file changes with StatReloader
INFO 2025-07-13 19:19:59,345 autoreload 19380 33904 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:20:01,276 autoreload 23036 34744 Watching for file changes with StatReloader
INFO 2025-07-13 19:20:17,570 autoreload 23036 34744 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:20:19,642 autoreload 2488 16824 Watching for file changes with StatReloader
INFO 2025-07-13 19:20:33,870 autoreload 2488 16824 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:20:35,813 autoreload 22724 26004 Watching for file changes with StatReloader
INFO 2025-07-13 19:20:48,749 autoreload 22724 26004 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:20:50,915 autoreload 8788 2856 Watching for file changes with StatReloader
INFO 2025-07-13 19:21:01,821 autoreload 8788 2856 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:21:03,792 autoreload 32376 10492 Watching for file changes with StatReloader
INFO 2025-07-13 19:21:36,392 autoreload 32196 37616 Watching for file changes with StatReloader
INFO 2025-07-13 19:24:02,056 autoreload 32196 37616 F:\PycharmProjects\django_ninja_cms\apps\cms\api.py changed, reloading.
INFO 2025-07-13 19:24:03,896 autoreload 32328 12892 Watching for file changes with StatReloader
INFO 2025-07-13 19:24:15,592 logging 32328 37716 Request started - ID: c76779e8-88dc-4f1c-994d-c02b17306b23 | Method: GET | Path: /api/docs/ | User:  | IP: 127.0.0.1
INFO 2025-07-13 19:24:15,650 logging 32328 37716 Request completed - ID: c76779e8-88dc-4f1c-994d-c02b17306b23 | Status: 200 | Duration: 0.058s
INFO 2025-07-13 19:24:15,651 basehttp 32328 37716 "GET /api/docs/ HTTP/1.1" 200 13774
INFO 2025-07-13 19:24:15,769 basehttp 32328 37716 "GET /static/ninja/swagger-ui.css HTTP/1.1" 304 0
INFO 2025-07-13 19:24:15,769 basehttp 32328 25120 "GET /static/ninja/swagger-ui-bundle.js HTTP/1.1" 304 0
INFO 2025-07-13 19:24:15,782 basehttp 32328 37716 "GET /static/ninja/swagger-ui-init.js HTTP/1.1" 304 0
INFO 2025-07-13 19:24:17,171 basehttp 32328 37716 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-07-13 19:24:17,269 basehttp 32328 37716 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-07-13 19:24:17,394 logging 32328 37716 Request started - ID: e58b221c-6750-446a-8d09-36a03feff9c7 | Method: GET | Path: /api/api/openapi.json | User:  | IP: 127.0.0.1
INFO 2025-07-13 19:24:17,617 basehttp 32328 25120 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-07-13 19:24:17,638 logging 32328 37716 Request completed - ID: e58b221c-6750-446a-8d09-36a03feff9c7 | Status: 200 | Duration: 0.243s
INFO 2025-07-13 19:24:17,640 basehttp 32328 37716 "GET /api/api/openapi.json HTTP/1.1" 200 74451
INFO 2025-07-13 19:24:19,695 basehttp 32328 37716 "GET /static/ninja/favicon.png HTTP/1.1" 304 0
INFO 2025-07-13 19:30:36,627 autoreload 32328 12892 F:\PycharmProjects\django_ninja_cms\apps\cms\models.py changed, reloading.
INFO 2025-07-13 19:30:38,526 autoreload 23924 32628 Watching for file changes with StatReloader
INFO 2025-07-13 19:32:13,626 autoreload 23924 32628 F:\PycharmProjects\django_ninja_cms\apps\cms\models.py changed, reloading.
INFO 2025-07-13 19:32:15,616 autoreload 14332 24996 Watching for file changes with StatReloader
INFO 2025-07-13 21:40:45,046 autoreload 14332 24996 F:\PycharmProjects\django_ninja_cms\apps\core\models.py changed, reloading.
INFO 2025-07-13 21:40:47,377 autoreload 27100 37484 Watching for file changes with StatReloader
INFO 2025-07-13 21:41:08,150 autoreload 27100 37484 F:\PycharmProjects\django_ninja_cms\apps\core\models.py changed, reloading.
INFO 2025-07-13 21:41:10,305 autoreload 33552 32908 Watching for file changes with StatReloader
INFO 2025-07-13 21:41:41,319 autoreload 33552 32908 F:\PycharmProjects\django_ninja_cms\apps\core\models.py changed, reloading.
INFO 2025-07-13 21:41:54,402 autoreload 5412 2476 Watching for file changes with StatReloader
INFO 2025-07-13 21:42:12,865 autoreload 5412 2476 F:\PycharmProjects\django_ninja_cms\apps\core\models.py changed, reloading.
INFO 2025-07-13 21:42:15,070 autoreload 4424 36336 Watching for file changes with StatReloader
INFO 2025-07-13 21:43:00,403 autoreload 4424 36336 F:\PycharmProjects\django_ninja_cms\apps\core\services.py changed, reloading.
INFO 2025-07-13 21:43:02,359 autoreload 37516 38196 Watching for file changes with StatReloader
INFO 2025-07-13 21:43:34,361 autoreload 37516 38196 F:\PycharmProjects\django_ninja_cms\apps\core\services.py changed, reloading.
INFO 2025-07-13 21:43:36,434 autoreload 36860 32984 Watching for file changes with StatReloader
INFO 2025-07-13 22:02:23,442 autoreload 36860 32984 F:\PycharmProjects\django_ninja_cms\core\settings\base.py changed, reloading.
INFO 2025-07-13 22:02:25,802 autoreload 20980 34796 Watching for file changes with StatReloader
