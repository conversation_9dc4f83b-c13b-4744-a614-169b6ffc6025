"""
Core app URL configuration.
"""

from django.urls import path
from .api import health_check, system_info, ping
from . import views

app_name = "core"

urlpatterns = [
    # API endpoints (legacy)
    path("api/health/", health_check, name="api_health_check"),
    path("api/info/", system_info, name="api_system_info"),
    path("api/ping/", ping, name="api_ping"),
    # 可视化模型设计器
    path("model-designer/", views.model_designer, name="model_designer"),
    # 仪表板
    path("dashboard/", views.dashboard, name="dashboard"),
    # 健康检查
    path("health/", views.HealthCheckView.as_view(), name="health_check"),
    # Webhook
    path("webhook/", views.WebhookView.as_view(), name="webhook"),
]
