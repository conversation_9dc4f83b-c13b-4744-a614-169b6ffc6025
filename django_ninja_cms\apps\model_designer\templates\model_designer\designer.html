<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可视化模型设计器</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        .model-designer {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .header {
            background: #409EFF;
            color: white;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .main-content {
            flex: 1;
            display: flex;
        }
        .sidebar {
            width: 300px;
            background: #f5f7fa;
            border-right: 1px solid #e4e7ed;
            overflow-y: auto;
        }
        .canvas-area {
            flex: 1;
            position: relative;
            background: #fafafa;
            overflow: auto;
        }
        .model-card {
            position: absolute;
            background: white;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            min-width: 200px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
            cursor: move;
        }
        .model-header {
            background: #409EFF;
            color: white;
            padding: 8px 12px;
            font-weight: bold;
            border-radius: 4px 4px 0 0;
        }
        .field-list {
            padding: 8px;
        }
        .field-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 12px;
        }
        .field-name {
            font-weight: 500;
        }
        .field-type {
            color: #909399;
        }
        .project-list {
            padding: 16px;
        }
        .project-item {
            padding: 12px;
            margin-bottom: 8px;
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .project-item:hover {
            border-color: #409EFF;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }
        .project-item.active {
            border-color: #409EFF;
            background: #ecf5ff;
        }
        .toolbar {
            padding: 16px;
            border-bottom: 1px solid #e4e7ed;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="model-designer">
            <!-- 头部 -->
            <div class="header">
                <h1 style="margin: 0;">可视化模型设计器</h1>
                <div>
                    <el-button type="primary" @click="showCreateProjectDialog = true">
                        <i class="el-icon-plus"></i> 新建项目
                    </el-button>
                    <el-button v-if="currentProject" @click="generateCode">
                        <i class="el-icon-cpu"></i> 生成代码
                    </el-button>
                    <el-button v-if="currentProject" @click="exportYaml">
                        <i class="el-icon-download"></i> 导出配置
                    </el-button>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="main-content">
                <!-- 侧边栏 -->
                <div class="sidebar">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <el-button size="small" @click="showImportDialog = true" style="width: 100%; margin-bottom: 8px;">
                            <i class="el-icon-upload"></i> 导入配置
                        </el-button>
                        <el-button v-if="currentProject" size="small" @click="showAddModelDialog = true" style="width: 100%;">
                            <i class="el-icon-plus"></i> 添加模型
                        </el-button>
                    </div>

                    <!-- 项目列表 -->
                    <div class="project-list">
                        <h3>项目列表</h3>
                        <div v-for="project in projects" :key="project.id" 
                             class="project-item" 
                             :class="{ active: currentProject && currentProject.id === project.id }"
                             @click="selectProject(project)">
                            <div style="font-weight: bold;">{{ project.name }}</div>
                            <div style="font-size: 12px; color: #909399;">
                                {{ project.app_name }} | {{ project.models_count }} 个模型
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 画布区域 -->
                <div class="canvas-area" ref="canvas">
                    <div v-if="!currentProject" style="display: flex; align-items: center; justify-content: center; height: 100%; color: #909399;">
                        <div style="text-align: center;">
                            <i class="el-icon-folder-opened" style="font-size: 64px; margin-bottom: 16px;"></i>
                            <div>请选择一个项目开始设计</div>
                        </div>
                    </div>

                    <!-- 模型卡片 -->
                    <div v-for="model in currentModels" :key="model.id" 
                         class="model-card"
                         :style="{ left: model.position_x + 'px', top: model.position_y + 'px' }"
                         @mousedown="startDrag(model, $event)"
                         @click="selectModel(model)">
                        <div class="model-header">
                            {{ model.name }}
                            <span v-if="model.verbose_name">({{ model.verbose_name }})</span>
                        </div>
                        <div class="field-list">
                            <div v-for="field in model.fields" :key="field.id" class="field-item">
                                <span class="field-name">{{ field.name }}</span>
                                <span class="field-type">{{ field.field_type }}</span>
                            </div>
                            <div v-if="!model.fields || model.fields.length === 0" style="color: #c0c4cc; text-align: center; padding: 8px;">
                                暂无字段
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div v-if="loading" class="loading-overlay">
            <el-loading-spinner size="large"></el-loading-spinner>
        </div>

        <!-- 对话框省略，与之前相同 -->
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    loading: false,
                    projects: [],
                    currentProject: null,
                    currentModels: [],
                    selectedModel: null,
                    
                    // 对话框状态
                    showCreateProjectDialog: false,
                    showImportDialog: false,
                    showAddModelDialog: false,
                    
                    // 表单数据
                    newProject: {
                        name: '',
                        app_name: '',
                        description: ''
                    },
                    importYamlContent: '',
                    newModel: {
                        name: '',
                        verbose_name: '',
                        description: ''
                    },
                    
                    // 拖拽相关
                    dragModel: null,
                    dragOffset: { x: 0, y: 0 }
                };
            },
            
            mounted() {
                this.loadProjects();
                this.setupDragEvents();
            },
            
            methods: {
                async loadProjects() {
                    this.loading = true;
                    try {
                        const response = await axios.get('/api/model-designer/projects');
                        this.projects = response.data;
                    } catch (error) {
                        ElMessage.error('加载项目列表失败');
                        console.error(error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                async selectProject(project) {
                    this.currentProject = project;
                    await this.loadModels();
                },
                
                async loadModels() {
                    if (!this.currentProject) return;
                    
                    this.loading = true;
                    try {
                        const response = await axios.get(`/api/model-designer/projects/${this.currentProject.id}/models`);
                        this.currentModels = response.data;
                        
                        // 加载每个模型的字段
                        for (let model of this.currentModels) {
                            const fieldsResponse = await axios.get(`/api/model-designer/models/${model.id}/fields`);
                            model.fields = fieldsResponse.data;
                        }
                    } catch (error) {
                        ElMessage.error('加载模型列表失败');
                        console.error(error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 其他方法与之前相同...
                setupDragEvents() {
                    // 拖拽事件处理
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
