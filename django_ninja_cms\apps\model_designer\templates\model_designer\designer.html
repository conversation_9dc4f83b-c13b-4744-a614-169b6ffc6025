<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>可视化模型设计器</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <style>
      body {
        margin: 0;
        font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
      }
      .model-designer {
        height: 100vh;
        display: flex;
        flex-direction: column;
      }
      .header {
        background: #409eff;
        color: white;
        padding: 16px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .main-content {
        flex: 1;
        display: flex;
      }
      .sidebar {
        width: 300px;
        background: #f5f7fa;
        border-right: 1px solid #e4e7ed;
        overflow-y: auto;
      }
      .canvas-area {
        flex: 1;
        position: relative;
        background: #fafafa;
        overflow: auto;
      }
      .model-card {
        position: absolute;
        background: white;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        min-width: 200px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
        cursor: move;
      }
      .model-header {
        background: #409eff;
        color: white;
        padding: 8px 12px;
        font-weight: bold;
        border-radius: 4px 4px 0 0;
      }
      .field-list {
        padding: 8px;
      }
      .field-item {
        display: flex;
        justify-content: space-between;
        padding: 4px 8px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;
      }
      .field-name {
        font-weight: 500;
      }
      .field-type {
        color: #909399;
      }
      .project-list {
        padding: 16px;
      }
      .project-item {
        padding: 12px;
        margin-bottom: 8px;
        background: white;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
      }
      .project-item:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }
      .project-item.active {
        border-color: #409eff;
        background: #ecf5ff;
      }
      .toolbar {
        padding: 16px;
        border-bottom: 1px solid #e4e7ed;
      }
      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="model-designer">
        <!-- 头部 -->
        <div class="header">
          <h1 style="margin: 0">可视化模型设计器</h1>
          <div>
            <el-button type="primary" @click="showCreateProjectDialog = true"> <i class="el-icon-plus"></i> 新建项目 </el-button>
            <el-button v-if="currentProject" @click="generateCode"> <i class="el-icon-cpu"></i> 生成代码 </el-button>
            <el-button v-if="currentProject" @click="exportYaml"> <i class="el-icon-download"></i> 导出配置 </el-button>
          </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
          <!-- 侧边栏 -->
          <div class="sidebar">
            <!-- 工具栏 -->
            <div class="toolbar">
              <el-button size="small" @click="showImportDialog = true" style="width: 100%; margin-bottom: 8px">
                <i class="el-icon-upload"></i> 导入配置
              </el-button>
              <el-button v-if="currentProject" size="small" @click="showAddModelDialog = true" style="width: 100%">
                <i class="el-icon-plus"></i> 添加模型
              </el-button>
            </div>

            <!-- 项目列表 -->
            <div class="project-list">
              <h3>项目列表</h3>
              <div
                v-for="project in projects"
                :key="project.id"
                class="project-item"
                :class="{ active: currentProject && currentProject.id === project.id }"
                @click="selectProject(project)"
              >
                <div style="font-weight: bold">[[ project.name ]]</div>
                <div style="font-size: 12px; color: #909399">[[ project.app_name ]] | [[ project.models_count ]] 个模型</div>
              </div>
            </div>
          </div>

          <!-- 画布区域 -->
          <div class="canvas-area" ref="canvas">
            <div v-if="!currentProject" style="display: flex; align-items: center; justify-content: center; height: 100%; color: #909399">
              <div style="text-align: center">
                <i class="el-icon-folder-opened" style="font-size: 64px; margin-bottom: 16px"></i>
                <div>请选择一个项目开始设计</div>
              </div>
            </div>

            <!-- 模型卡片 -->
            <div
              v-for="model in currentModels"
              :key="model.id"
              class="model-card"
              :style="{ left: model.position_x + 'px', top: model.position_y + 'px' }"
              @mousedown="startDrag(model, $event)"
              @click="selectModel(model)"
            >
              <div class="model-header">
                [[ model.name ]]
                <span v-if="model.verbose_name">([[ model.verbose_name ]])</span>
              </div>
              <div class="field-list">
                <div v-for="field in model.fields" :key="field.id" class="field-item">
                  <span class="field-name">[[ field.name ]]</span>
                  <span class="field-type">[[ field.field_type ]]</span>
                </div>
                <div v-if="!model.fields || model.fields.length === 0" style="color: #c0c4cc; text-align: center; padding: 8px">
                  暂无字段
                  <br />
                  <el-button size="small" type="text" @click="showAddFieldDialog(model)">添加字段</el-button>
                </div>
                <div v-else style="text-align: center; padding: 4px">
                  <el-button size="small" type="text" @click="showAddFieldDialog(model)">+ 添加字段</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载遮罩 -->
      <div v-if="loading" class="loading-overlay">
        <el-loading-spinner size="large"></el-loading-spinner>
      </div>

      <!-- 创建项目对话框 -->
      <el-dialog v-model="showCreateProjectDialog" title="创建新项目" width="500px">
        <el-form :model="newProject" label-width="100px">
          <el-form-item label="项目名称" required>
            <el-input v-model="newProject.name" placeholder="请输入项目名称"></el-input>
          </el-form-item>
          <el-form-item label="应用名称" required>
            <el-input v-model="newProject.app_name" placeholder="请输入应用名称"></el-input>
          </el-form-item>
          <el-form-item label="项目描述">
            <el-input v-model="newProject.description" type="textarea" placeholder="请输入项目描述"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showCreateProjectDialog = false">取消</el-button>
            <el-button type="primary" @click="createProject">创建</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 导入YAML对话框 -->
      <el-dialog v-model="showImportDialog" title="导入YAML配置" width="600px">
        <el-form label-width="100px">
          <el-form-item label="YAML内容" required>
            <el-input v-model="importYamlContent" type="textarea" :rows="10" placeholder="请粘贴YAML配置内容"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showImportDialog = false">取消</el-button>
            <el-button type="primary" @click="importFromYaml">导入</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加模型对话框 -->
      <el-dialog v-model="showAddModelDialog" title="添加模型" width="500px">
        <el-form :model="newModel" label-width="100px">
          <el-form-item label="模型名称" required>
            <el-input v-model="newModel.name" placeholder="请输入模型名称"></el-input>
          </el-form-item>
          <el-form-item label="显示名称">
            <el-input v-model="newModel.verbose_name" placeholder="请输入显示名称"></el-input>
          </el-form-item>
          <el-form-item label="模型描述">
            <el-input v-model="newModel.description" type="textarea" placeholder="请输入模型描述"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddModelDialog = false">取消</el-button>
            <el-button type="primary" @click="createModel">创建</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加字段对话框 -->
      <el-dialog v-model="showAddFieldDialog" title="添加字段" width="600px">
        <el-form :model="newField" label-width="120px">
          <el-form-item label="字段名称" required>
            <el-input v-model="newField.name" placeholder="请输入字段名称"></el-input>
          </el-form-item>
          <el-form-item label="字段类型" required>
            <el-select v-model="newField.field_type" placeholder="请选择字段类型" style="width: 100%">
              <el-option label="字符串 (CharField)" value="CharField"></el-option>
              <el-option label="文本 (TextField)" value="TextField"></el-option>
              <el-option label="整数 (IntegerField)" value="IntegerField"></el-option>
              <el-option label="浮点数 (FloatField)" value="FloatField"></el-option>
              <el-option label="小数 (DecimalField)" value="DecimalField"></el-option>
              <el-option label="布尔值 (BooleanField)" value="BooleanField"></el-option>
              <el-option label="日期 (DateField)" value="DateField"></el-option>
              <el-option label="日期时间 (DateTimeField)" value="DateTimeField"></el-option>
              <el-option label="时间 (TimeField)" value="TimeField"></el-option>
              <el-option label="邮箱 (EmailField)" value="EmailField"></el-option>
              <el-option label="URL (URLField)" value="URLField"></el-option>
              <el-option label="外键 (ForeignKey)" value="ForeignKey"></el-option>
              <el-option label="多对多 (ManyToManyField)" value="ManyToManyField"></el-option>
              <el-option label="一对一 (OneToOneField)" value="OneToOneField"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="显示名称">
            <el-input v-model="newField.verbose_name" placeholder="请输入显示名称"></el-input>
          </el-form-item>
          <el-form-item label="帮助文本">
            <el-input v-model="newField.help_text" placeholder="请输入帮助文本"></el-input>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="允许为空">
                <el-switch v-model="newField.null"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="允许空白">
                <el-switch v-model="newField.blank"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="唯一">
                <el-switch v-model="newField.unique"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item v-if="newField.field_type === 'CharField'" label="最大长度">
            <el-input-number v-model="newField.max_length" :min="1" :max="1000" placeholder="200"></el-input-number>
          </el-form-item>
          <el-form-item v-if="newField.field_type === 'DecimalField'" label="最大位数">
            <el-input-number v-model="newField.max_digits" :min="1" :max="20" placeholder="10"></el-input-number>
          </el-form-item>
          <el-form-item v-if="newField.field_type === 'DecimalField'" label="小数位数">
            <el-input-number v-model="newField.decimal_places" :min="0" :max="10" placeholder="2"></el-input-number>
          </el-form-item>
          <el-form-item v-if="['ForeignKey', 'ManyToManyField', 'OneToOneField'].includes(newField.field_type)" label="关联模型">
            <el-input v-model="newField.related_model" placeholder="例如: User, Category"></el-input>
          </el-form-item>
          <el-form-item label="默认值">
            <el-input v-model="newField.default_value" placeholder="请输入默认值"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddFieldDialog = false">取消</el-button>
            <el-button type="primary" @click="createField">创建</el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <script>
      const { createApp } = Vue
      const { ElMessage, ElMessageBox } = ElementPlus

      createApp({
        delimiters: ['[[', ']]'],
        data() {
          return {
            loading: false,
            projects: [],
            currentProject: null,
            currentModels: [],
            selectedModel: null,

            // 对话框状态
            showCreateProjectDialog: false,
            showImportDialog: false,
            showAddModelDialog: false,
            showAddFieldDialog: false,

            // 表单数据
            newProject: {
              name: '',
              app_name: '',
              description: '',
            },
            importYamlContent: '',
            newModel: {
              name: '',
              verbose_name: '',
              description: '',
            },
            newField: {
              name: '',
              field_type: '',
              verbose_name: '',
              help_text: '',
              null: false,
              blank: false,
              unique: false,
              max_length: null,
              max_digits: null,
              decimal_places: null,
              related_model: '',
              default_value: '',
            },
            currentModelForField: null,

            // 拖拽相关
            dragModel: null,
            dragOffset: { x: 0, y: 0 },
          }
        },

        mounted() {
          this.loadProjects()
          this.setupDragEvents()
        },

        methods: {
          async loadProjects() {
            this.loading = true
            try {
              const response = await axios.get('/api/model-designer/projects')
              this.projects = response.data
            } catch (error) {
              ElMessage.error('加载项目列表失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          async selectProject(project) {
            this.currentProject = project
            await this.loadModels()
          },

          async loadModels() {
            if (!this.currentProject) return

            this.loading = true
            try {
              const response = await axios.get(`/api/model-designer/projects/${this.currentProject.id}/models`)
              this.currentModels = response.data

              // 加载每个模型的字段
              for (let model of this.currentModels) {
                const fieldsResponse = await axios.get(`/api/model-designer/models/${model.id}/fields`)
                model.fields = fieldsResponse.data
              }
            } catch (error) {
              ElMessage.error('加载模型列表失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          async createProject() {
            if (!this.newProject.name || !this.newProject.app_name) {
              ElMessage.error('请填写项目名称和应用名称')
              return
            }

            this.loading = true
            try {
              const response = await axios.post('/api/model-designer/projects', this.newProject)
              this.projects.push(response.data)
              this.showCreateProjectDialog = false
              this.newProject = { name: '', app_name: '', description: '' }
              ElMessage.success('项目创建成功')
            } catch (error) {
              ElMessage.error('项目创建失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          async createModel() {
            if (!this.newModel.name) {
              ElMessage.error('请填写模型名称')
              return
            }

            this.loading = true
            try {
              const response = await axios.post(`/api/model-designer/projects/${this.currentProject.id}/models`, this.newModel)
              response.data.fields = []
              this.currentModels.push(response.data)
              this.showAddModelDialog = false
              this.newModel = { name: '', verbose_name: '', description: '' }
              ElMessage.success('模型创建成功')
            } catch (error) {
              ElMessage.error('模型创建失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          async importFromYaml() {
            if (!this.importYamlContent) {
              ElMessage.error('请输入YAML内容')
              return
            }

            this.loading = true
            try {
              const response = await axios.post('/api/model-designer/import-yaml', {
                yaml_content: this.importYamlContent,
              })
              this.projects.push(response.data)
              this.showImportDialog = false
              this.importYamlContent = ''
              ElMessage.success('YAML导入成功')
            } catch (error) {
              ElMessage.error('YAML导入失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          async exportYaml() {
            if (!this.currentProject) {
              ElMessage.error('请先选择一个项目')
              return
            }

            try {
              const response = await axios.get(`/api/model-designer/projects/${this.currentProject.id}/export-yaml`)
              const blob = new Blob([response.data.yaml_content], { type: 'text/yaml' })
              const url = window.URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `${this.currentProject.name}.yaml`
              a.click()
              window.URL.revokeObjectURL(url)
              ElMessage.success('YAML导出成功')
            } catch (error) {
              ElMessage.error('YAML导出失败')
              console.error(error)
            }
          },

          async generateCode() {
            if (!this.currentProject) {
              ElMessage.error('请先选择一个项目')
              return
            }

            this.loading = true
            try {
              const response = await axios.post(`/api/model-designer/projects/${this.currentProject.id}/generate`, {
                full: true,
                with_frontend: false,
                output_dir: 'apps',
              })
              ElMessage.success('代码生成成功')
              console.log('生成结果:', response.data)
            } catch (error) {
              ElMessage.error('代码生成失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          selectModel(model) {
            this.selectedModel = model
          },

          showAddFieldDialog(model) {
            this.currentModelForField = model
            this.showAddFieldDialog = true
          },

          async createField() {
            if (!this.newField.name || !this.newField.field_type) {
              ElMessage.error('请填写字段名称和类型')
              return
            }

            this.loading = true
            try {
              const response = await axios.post(`/api/model-designer/models/${this.currentModelForField.id}/fields`, this.newField)
              this.currentModelForField.fields.push(response.data)
              this.showAddFieldDialog = false
              this.resetNewField()
              ElMessage.success('字段创建成功')
            } catch (error) {
              ElMessage.error('字段创建失败')
              console.error(error)
            } finally {
              this.loading = false
            }
          },

          resetNewField() {
            this.newField = {
              name: '',
              field_type: '',
              verbose_name: '',
              help_text: '',
              null: false,
              blank: false,
              unique: false,
              max_length: null,
              max_digits: null,
              decimal_places: null,
              related_model: '',
              default_value: '',
            }
          },

          startDrag(model, event) {
            this.dragModel = model
            const rect = this.$refs.canvas.getBoundingClientRect()
            this.dragOffset = {
              x: event.clientX - rect.left - model.position_x,
              y: event.clientY - rect.top - model.position_y,
            }
          },

          setupDragEvents() {
            // 拖拽事件处理
            document.addEventListener('mousemove', (event) => {
              if (this.dragModel) {
                const rect = this.$refs.canvas.getBoundingClientRect()
                this.dragModel.position_x = event.clientX - rect.left - this.dragOffset.x
                this.dragModel.position_y = event.clientY - rect.top - this.dragOffset.y
              }
            })

            document.addEventListener('mouseup', () => {
              if (this.dragModel) {
                // 这里可以添加保存位置的API调用
                this.dragModel = null
              }
            })
          },
        },
      })
        .use(ElementPlus)
        .mount('#app')
    </script>
  </body>
</html>
