"""
Model Designer API endpoints.
"""

from typing import List
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from ninja import Router, Query

from .models import (
    ModelProject,
    ModelDesign,
    FieldDesign,
    CodeGenerationHistory,
    ModelTemplate,
    FieldPreset,
)
from .schemas import (
    ModelProjectCreateSchema,
    ModelProjectUpdateSchema,
    ModelProjectSchema,
    ModelProjectListSchema,
    ModelDesignCreateSchema,
    ModelDesignUpdateSchema,
    ModelDesignSchema,
    FieldDesignCreateSchema,
    FieldDesignUpdateSchema,
    FieldDesignSchema,
    CodeGenerationRequestSchema,
    CodeGenerationHistorySchema,
    YamlImportSchema,
    YamlExportSchema,
    ExistingModelSchema,
    ModelTemplateSchema,
    FieldPresetSchema,
    MessageSchema,
    ErrorSchema,
)
from .services import ModelProjectService, CodeGenerationService

model_designer_router = Router()


# 模型项目管理 API
@model_designer_router.get("/projects", response=List[ModelProjectListSchema])
def list_model_projects(request):
    """获取模型项目列表"""
    # 暂时返回所有活跃项目，不过滤用户
    projects = ModelProject.objects.filter(is_active=True)
    return [
        {**project.__dict__, "models_count": project.get_models_count()}
        for project in projects
    ]


@model_designer_router.post("/projects", response=ModelProjectSchema)
def create_model_project(request, data: ModelProjectCreateSchema):
    """创建模型项目"""
    try:
        # 获取默认用户或创建匿名用户
        from django.contrib.auth import get_user_model

        User = get_user_model()

        # 尝试获取认证用户，如果没有则使用默认用户
        if hasattr(request, "user") and request.user.is_authenticated:
            owner = request.user
        else:
            # 获取或创建默认用户
            owner, created = User.objects.get_or_create(
                email="<EMAIL>",
                defaults={
                    "username": "default_user",
                    "first_name": "Default",
                    "last_name": "User",
                    "is_active": True,
                },
            )

        project = ModelProjectService.create_project(
            name=data.name,
            app_name=data.app_name,
            owner=owner,
            description=data.description,
        )
        return {**project.__dict__, "models_count": project.get_models_count()}
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@model_designer_router.get("/projects/{project_id}", response=ModelProjectSchema)
def get_model_project(request, project_id: int):
    """获取模型项目详情"""
    project = get_object_or_404(ModelProject, id=project_id)
    return {**project.__dict__, "models_count": project.get_models_count()}


@model_designer_router.put("/projects/{project_id}", response=ModelProjectSchema)
def update_model_project(request, project_id: int, data: ModelProjectUpdateSchema):
    """更新模型项目"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)

    project.save()
    return {**project.__dict__, "models_count": project.get_models_count()}


@model_designer_router.delete("/projects/{project_id}", response=MessageSchema)
def delete_model_project(request, project_id: int):
    """删除模型项目"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    project.delete()
    return {"message": "项目删除成功"}


# 模型设计管理 API
@model_designer_router.get(
    "/projects/{project_id}/models", response=List[ModelDesignSchema]
)
def list_model_designs(request, project_id: int):
    """获取项目的模型设计列表"""
    project = get_object_or_404(ModelProject, id=project_id)
    models = project.model_designs.all()
    return [
        {**model.__dict__, "fields_count": model.get_fields_count()} for model in models
    ]


@model_designer_router.post("/projects/{project_id}/models", response=ModelDesignSchema)
def create_model_design(request, project_id: int, data: ModelDesignCreateSchema):
    """创建模型设计"""
    project = get_object_or_404(ModelProject, id=project_id)

    model_design = ModelDesign.objects.create(project=project, **data.dict())
    return {**model_design.__dict__, "fields_count": model_design.get_fields_count()}


@model_designer_router.get("/models/{model_id}", response=ModelDesignSchema)
def get_model_design(request, model_id: int):
    """获取模型设计详情"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )
    return {**model_design.__dict__, "fields_count": model_design.get_fields_count()}


@model_designer_router.put("/models/{model_id}", response=ModelDesignSchema)
def update_model_design(request, model_id: int, data: ModelDesignUpdateSchema):
    """更新模型设计"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(model_design, field, value)

    model_design.save()
    return {**model_design.__dict__, "fields_count": model_design.get_fields_count()}


@model_designer_router.delete("/models/{model_id}", response=MessageSchema)
def delete_model_design(request, model_id: int):
    """删除模型设计"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )
    model_design.delete()
    return {"message": "模型删除成功"}


# 字段设计管理 API
@model_designer_router.get(
    "/models/{model_id}/fields", response=List[FieldDesignSchema]
)
def list_field_designs(request, model_id: int):
    """获取模型的字段设计列表"""
    model_design = get_object_or_404(ModelDesign, id=model_id)
    fields = model_design.field_designs.all()
    return [field.__dict__ for field in fields]


@model_designer_router.post("/models/{model_id}/fields", response=FieldDesignSchema)
def create_field_design(request, model_id: int, data: FieldDesignCreateSchema):
    """创建字段设计"""
    model_design = get_object_or_404(ModelDesign, id=model_id)

    field_design = FieldDesign.objects.create(model_design=model_design, **data.dict())
    return field_design.__dict__


@model_designer_router.get("/fields/{field_id}", response=FieldDesignSchema)
def get_field_design(request, field_id: int):
    """获取字段设计详情"""
    field_design = get_object_or_404(
        FieldDesign, id=field_id, model_design__project__owner=request.user
    )
    return field_design.__dict__


@model_designer_router.put("/fields/{field_id}", response=FieldDesignSchema)
def update_field_design(request, field_id: int, data: FieldDesignUpdateSchema):
    """更新字段设计"""
    field_design = get_object_or_404(
        FieldDesign, id=field_id, model_design__project__owner=request.user
    )

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(field_design, field, value)

    field_design.save()
    return field_design.__dict__


@model_designer_router.delete("/fields/{field_id}", response=MessageSchema)
def delete_field_design(request, field_id: int):
    """删除字段设计"""
    field_design = get_object_or_404(
        FieldDesign, id=field_id, model_design__project__owner=request.user
    )
    field_design.delete()
    return {"message": "字段删除成功"}


# YAML 导入导出 API
@model_designer_router.post("/import-yaml", response=ModelProjectSchema)
def import_from_yaml(request, data: YamlImportSchema):
    """从 YAML 配置导入项目"""
    try:
        # 获取默认用户
        from django.contrib.auth import get_user_model

        User = get_user_model()

        if hasattr(request, "user") and request.user.is_authenticated:
            owner = request.user
        else:
            owner, created = User.objects.get_or_create(
                email="<EMAIL>",
                defaults={
                    "username": "default_user",
                    "first_name": "Default",
                    "last_name": "User",
                    "is_active": True,
                },
            )

        project = ModelProjectService.import_from_yaml(data.yaml_content, owner)
        return {**project.__dict__, "models_count": project.get_models_count()}
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@model_designer_router.get(
    "/projects/{project_id}/export-yaml", response=YamlExportSchema
)
def export_to_yaml(request, project_id: int):
    """导出项目为 YAML 配置"""
    project = get_object_or_404(ModelProject, id=project_id)
    yaml_content = ModelProjectService.export_project_to_yaml(project)
    return {"yaml_content": yaml_content}


# 代码生成 API
@model_designer_router.post(
    "/projects/{project_id}/generate", response=CodeGenerationHistorySchema
)
def generate_code(request, project_id: int, data: CodeGenerationRequestSchema):
    """生成代码"""
    project = get_object_or_404(ModelProject, id=project_id)

    # 获取默认用户
    from django.contrib.auth import get_user_model

    User = get_user_model()

    if hasattr(request, "user") and request.user.is_authenticated:
        user = request.user
    else:
        user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "username": "default_user",
                "first_name": "Default",
                "last_name": "User",
                "is_active": True,
            },
        )

    options = data.dict()
    history = CodeGenerationService.generate_code(project, user, options)
    return history.__dict__


@model_designer_router.get(
    "/projects/{project_id}/generation-history",
    response=List[CodeGenerationHistorySchema],
)
def list_generation_history(request, project_id: int):
    """获取代码生成历史"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    history = project.generation_history.all()[:20]  # 最近20条记录
    return [h.__dict__ for h in history]


@model_designer_router.get(
    "/generation-history/{history_id}", response=CodeGenerationHistorySchema
)
def get_generation_history(request, history_id: int):
    """获取代码生成历史详情"""
    history = get_object_or_404(CodeGenerationHistory, id=history_id, user=request.user)
    return history.__dict__


# 现有模型信息 API
@model_designer_router.get("/existing-models", response=List[ExistingModelSchema])
def list_existing_models(request):
    """获取现有的 Django 模型信息"""
    models_info = ModelProjectService.get_existing_models()
    return models_info


# 模板和预设 API
@model_designer_router.get("/templates", response=List[ModelTemplateSchema])
def list_model_templates(request, category: str = Query(None)):
    """获取模型模板列表"""
    templates = ModelTemplate.objects.filter(is_active=True)
    if category:
        templates = templates.filter(category=category)
    return [t.__dict__ for t in templates]


@model_designer_router.get("/templates/{template_id}", response=ModelTemplateSchema)
def get_model_template(request, template_id: int):
    """获取模型模板详情"""
    template = get_object_or_404(ModelTemplate, id=template_id, is_active=True)
    return template.__dict__


@model_designer_router.post("/templates/{template_id}/use", response=ModelProjectSchema)
def use_model_template(request, template_id: int):
    """使用模型模板创建项目"""
    template = get_object_or_404(ModelTemplate, id=template_id, is_active=True)

    try:
        # 增加使用次数
        template.increment_usage()

        # 从模板配置创建项目
        config = template.template_config
        import yaml

        yaml_content = yaml.dump(config, default_flow_style=False, allow_unicode=True)

        project = ModelProjectService.import_from_yaml(yaml_content, request.user)
        project.name = f"{template.name}_project"
        project.save()

        return {**project.__dict__, "models_count": project.get_models_count()}
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@model_designer_router.get("/field-presets", response=List[FieldPresetSchema])
def list_field_presets(request, tags: str = Query(None)):
    """获取字段预设列表"""
    presets = FieldPreset.objects.filter(is_active=True)
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        for tag in tag_list:
            presets = presets.filter(tags__contains=tag)
    return [p.__dict__ for p in presets]


@model_designer_router.get("/field-presets/{preset_id}", response=FieldPresetSchema)
def get_field_preset(request, preset_id: int):
    """获取字段预设详情"""
    preset = get_object_or_404(FieldPreset, id=preset_id, is_active=True)
    return preset.__dict__


@model_designer_router.post("/field-presets/{preset_id}/use")
def use_field_preset(request, preset_id: int, model_id: int):
    """使用字段预设添加字段到模型"""
    preset = get_object_or_404(FieldPreset, id=preset_id, is_active=True)
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )

    try:
        # 增加使用次数
        preset.increment_usage()

        # 添加预设字段到模型
        created_fields = []
        for field_config in preset.fields_config:
            field_design = FieldDesign.objects.create(
                model_design=model_design, **field_config
            )
            created_fields.append(field_design.__dict__)

        return {
            "message": f"成功添加 {len(created_fields)} 个字段",
            "fields": created_fields,
        }
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


# 字段类型和选项 API
@model_designer_router.get("/field-types")
def get_field_types(request):
    """获取支持的字段类型"""
    from .models import FieldDesign

    return {
        "field_types": [
            {"value": choice[0], "label": choice[1]}
            for choice in FieldDesign.FIELD_TYPE_CHOICES
        ],
        "on_delete_choices": [
            {"value": choice[0], "label": choice[1]}
            for choice in FieldDesign.ON_DELETE_CHOICES
        ],
    }
