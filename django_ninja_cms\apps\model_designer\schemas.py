"""
Pydantic schemas for the model designer API.
"""
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from ninja import Schema


# 基础 Schema
class BaseSchema(Schema):
    """基础 Schema"""
    created_at: datetime
    updated_at: datetime


# 模型项目相关 Schema
class ModelProjectCreateSchema(Schema):
    """创建模型项目的 Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="项目名称")
    app_name: str = Field(..., min_length=1, max_length=50, description="应用名称")
    description: Optional[str] = Field("", description="项目描述")
    config: Optional[Dict[str, Any]] = Field({}, description="项目配置")


class ModelProjectUpdateSchema(Schema):
    """更新模型项目的 Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    config: Optional[Dict[str, Any]] = Field(None, description="项目配置")
    is_active: Optional[bool] = Field(None, description="是否激活")


class ModelProjectSchema(BaseSchema):
    """模型项目的 Schema"""
    id: int
    name: str
    app_name: str
    description: str
    owner_id: int
    is_active: bool
    version: str
    config: Dict[str, Any]
    models_count: int = Field(description="模型数量")


class ModelProjectListSchema(Schema):
    """模型项目列表的 Schema"""
    id: int
    name: str
    app_name: str
    description: str
    is_active: bool
    version: str
    models_count: int
    created_at: datetime
    updated_at: datetime


# 模型设计相关 Schema
class ModelDesignCreateSchema(Schema):
    """创建模型设计的 Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="模型名称")
    description: Optional[str] = Field("", description="模型描述")
    verbose_name: Optional[str] = Field("", description="显示名称")
    verbose_name_plural: Optional[str] = Field("", description="复数显示名称")
    db_table: Optional[str] = Field("", description="数据库表名")
    ordering: Optional[List[str]] = Field([], description="排序字段")
    indexes: Optional[List[Dict[str, Any]]] = Field([], description="索引")
    constraints: Optional[List[Dict[str, Any]]] = Field([], description="约束")
    permissions: Optional[List[List[str]]] = Field([], description="权限")
    position_x: Optional[int] = Field(0, description="X坐标")
    position_y: Optional[int] = Field(0, description="Y坐标")


class ModelDesignUpdateSchema(Schema):
    """更新模型设计的 Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    verbose_name: Optional[str] = Field(None, description="显示名称")
    verbose_name_plural: Optional[str] = Field(None, description="复数显示名称")
    db_table: Optional[str] = Field(None, description="数据库表名")
    ordering: Optional[List[str]] = Field(None, description="排序字段")
    indexes: Optional[List[Dict[str, Any]]] = Field(None, description="索引")
    constraints: Optional[List[Dict[str, Any]]] = Field(None, description="约束")
    permissions: Optional[List[List[str]]] = Field(None, description="权限")
    position_x: Optional[int] = Field(None, description="X坐标")
    position_y: Optional[int] = Field(None, description="Y坐标")


class ModelDesignSchema(BaseSchema):
    """模型设计的 Schema"""
    id: int
    project_id: int
    name: str
    description: str
    verbose_name: str
    verbose_name_plural: str
    db_table: str
    ordering: List[str]
    indexes: List[Dict[str, Any]]
    constraints: List[Dict[str, Any]]
    permissions: List[List[str]]
    position_x: int
    position_y: int
    fields_count: int = Field(description="字段数量")


# 字段设计相关 Schema
class FieldDesignCreateSchema(Schema):
    """创建字段设计的 Schema"""
    name: str = Field(..., min_length=1, max_length=100, description="字段名称")
    field_type: str = Field(..., description="字段类型")
    description: Optional[str] = Field("", description="字段描述")
    null: Optional[bool] = Field(False, description="允许为空")
    blank: Optional[bool] = Field(False, description="允许空白")
    unique: Optional[bool] = Field(False, description="唯一约束")
    db_index: Optional[bool] = Field(False, description="数据库索引")
    max_length: Optional[int] = Field(None, description="最大长度")
    max_digits: Optional[int] = Field(None, description="最大位数")
    decimal_places: Optional[int] = Field(None, description="小数位数")
    default_value: Optional[str] = Field("", description="默认值")
    validators: Optional[List[Dict[str, Any]]] = Field([], description="验证器")
    help_text: Optional[str] = Field("", description="帮助文本")
    verbose_name: Optional[str] = Field("", description="显示名称")
    related_model: Optional[str] = Field("", description="关联模型")
    on_delete: Optional[str] = Field("", description="删除行为")
    related_name: Optional[str] = Field("", description="反向关联名称")
    upload_to: Optional[str] = Field("", description="上传路径")
    choices: Optional[List[List[str]]] = Field([], description="选择项")
    order: Optional[int] = Field(0, description="排序")


class FieldDesignUpdateSchema(Schema):
    """更新字段设计的 Schema"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="字段名称")
    field_type: Optional[str] = Field(None, description="字段类型")
    description: Optional[str] = Field(None, description="字段描述")
    null: Optional[bool] = Field(None, description="允许为空")
    blank: Optional[bool] = Field(None, description="允许空白")
    unique: Optional[bool] = Field(None, description="唯一约束")
    db_index: Optional[bool] = Field(None, description="数据库索引")
    max_length: Optional[int] = Field(None, description="最大长度")
    max_digits: Optional[int] = Field(None, description="最大位数")
    decimal_places: Optional[int] = Field(None, description="小数位数")
    default_value: Optional[str] = Field(None, description="默认值")
    validators: Optional[List[Dict[str, Any]]] = Field(None, description="验证器")
    help_text: Optional[str] = Field(None, description="帮助文本")
    verbose_name: Optional[str] = Field(None, description="显示名称")
    related_model: Optional[str] = Field(None, description="关联模型")
    on_delete: Optional[str] = Field(None, description="删除行为")
    related_name: Optional[str] = Field(None, description="反向关联名称")
    upload_to: Optional[str] = Field(None, description="上传路径")
    choices: Optional[List[List[str]]] = Field(None, description="选择项")
    order: Optional[int] = Field(None, description="排序")


class FieldDesignSchema(BaseSchema):
    """字段设计的 Schema"""
    id: int
    model_design_id: int
    name: str
    field_type: str
    description: str
    null: bool
    blank: bool
    unique: bool
    db_index: bool
    max_length: Optional[int]
    max_digits: Optional[int]
    decimal_places: Optional[int]
    default_value: str
    validators: List[Dict[str, Any]]
    help_text: str
    verbose_name: str
    related_model: str
    on_delete: str
    related_name: str
    upload_to: str
    choices: List[List[str]]
    order: int


# 代码生成相关 Schema
class CodeGenerationRequestSchema(Schema):
    """代码生成请求的 Schema"""
    full: Optional[bool] = Field(False, description="生成完整功能")
    with_frontend: Optional[bool] = Field(False, description="生成前端组件")
    output_dir: Optional[str] = Field("apps", description="输出目录")


class CodeGenerationHistorySchema(BaseSchema):
    """代码生成历史的 Schema"""
    id: int
    project_id: int
    user_id: int
    status: str
    generate_full: bool
    generate_frontend: bool
    output_directory: str
    generated_files: List[str]
    error_message: str
    execution_time: Optional[float]
    config_snapshot: Dict[str, Any]


# YAML 导入导出相关 Schema
class YamlImportSchema(Schema):
    """YAML 导入的 Schema"""
    yaml_content: str = Field(..., description="YAML 配置内容")


class YamlExportSchema(Schema):
    """YAML 导出的 Schema"""
    yaml_content: str = Field(..., description="YAML 配置内容")


# 现有模型信息 Schema
class ExistingModelFieldSchema(Schema):
    """现有模型字段信息的 Schema"""
    name: str
    type: str
    verbose_name: str
    help_text: str
    null: bool
    blank: bool
    related_model: Optional[str] = None


class ExistingModelSchema(Schema):
    """现有模型信息的 Schema"""
    app_name: str
    model_name: str
    verbose_name: str
    fields: List[ExistingModelFieldSchema]


# 模板相关 Schema
class ModelTemplateSchema(BaseSchema):
    """模型模板的 Schema"""
    id: int
    name: str
    description: str
    category: str
    template_config: Dict[str, Any]
    preview_image: str
    usage_count: int
    is_active: bool
    is_featured: bool


class FieldPresetSchema(BaseSchema):
    """字段预设的 Schema"""
    id: int
    name: str
    description: str
    fields_config: List[Dict[str, Any]]
    tags: List[str]
    usage_count: int
    is_active: bool


# 响应 Schema
class MessageSchema(Schema):
    """消息响应的 Schema"""
    message: str
    success: bool = True


class ErrorSchema(Schema):
    """错误响应的 Schema"""
    error: str
    details: Optional[Dict[str, Any]] = None
    success: bool = False
