"""
Model Designer admin configuration.
"""
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from .models import (
    ModelProject, ModelDesign, FieldDesign, 
    CodeGenerationHistory, ModelTemplate, FieldPreset
)


class ModelDesignInline(admin.TabularInline):
    model = ModelDesign
    extra = 0
    fields = ('name', 'verbose_name', 'position_x', 'position_y')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(ModelProject)
class ModelProjectAdmin(admin.ModelAdmin):
    """
    Admin configuration for ModelProject model.
    """
    list_display = ('name', 'app_name', 'owner', 'is_active', 'version', 'models_count_display', 'created_at')
    list_filter = ('is_active', 'owner', 'created_at')
    search_fields = ('name', 'app_name', 'description')
    readonly_fields = ('created_at', 'updated_at', 'models_count_display')
    inlines = [ModelDesignInline]
    
    fieldsets = (
        (_('Basic Info'), {
            'fields': ('name', 'app_name', 'owner', 'description', 'is_active', 'version')
        }),
        (_('Configuration'), {
            'fields': ('config',),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('models_count_display',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def models_count_display(self, obj):
        return obj.get_models_count()
    models_count_display.short_description = _('Models Count')


class FieldDesignInline(admin.TabularInline):
    model = FieldDesign
    extra = 0
    fields = ('name', 'field_type', 'null', 'blank', 'unique', 'order')
    ordering = ('order', 'name')


@admin.register(ModelDesign)
class ModelDesignAdmin(admin.ModelAdmin):
    """
    Admin configuration for ModelDesign model.
    """
    list_display = ('name', 'project', 'verbose_name', 'fields_count_display', 'created_at')
    list_filter = ('project', 'created_at')
    search_fields = ('name', 'description', 'verbose_name')
    readonly_fields = ('created_at', 'updated_at', 'fields_count_display')
    inlines = [FieldDesignInline]
    
    fieldsets = (
        (_('Basic Info'), {
            'fields': ('project', 'name', 'description')
        }),
        (_('Display Options'), {
            'fields': ('verbose_name', 'verbose_name_plural', 'db_table'),
        }),
        (_('Model Options'), {
            'fields': ('ordering', 'indexes', 'constraints', 'permissions'),
            'classes': ('collapse',)
        }),
        (_('Position'), {
            'fields': ('position_x', 'position_y'),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('fields_count_display',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def fields_count_display(self, obj):
        return obj.get_fields_count()
    fields_count_display.short_description = _('Fields Count')


@admin.register(FieldDesign)
class FieldDesignAdmin(admin.ModelAdmin):
    """
    Admin configuration for FieldDesign model.
    """
    list_display = ('name', 'model_design', 'field_type', 'null', 'blank', 'unique', 'order')
    list_filter = ('field_type', 'null', 'blank', 'unique', 'model_design__project')
    search_fields = ('name', 'description', 'verbose_name', 'model_design__name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Basic Info'), {
            'fields': ('model_design', 'name', 'field_type', 'description', 'order')
        }),
        (_('Basic Options'), {
            'fields': ('null', 'blank', 'unique', 'db_index'),
        }),
        (_('String/Number Options'), {
            'fields': ('max_length', 'max_digits', 'decimal_places'),
            'classes': ('collapse',)
        }),
        (_('Default & Validation'), {
            'fields': ('default_value', 'validators'),
            'classes': ('collapse',)
        }),
        (_('Display'), {
            'fields': ('help_text', 'verbose_name'),
            'classes': ('collapse',)
        }),
        (_('Relationship Options'), {
            'fields': ('related_model', 'on_delete', 'related_name'),
            'classes': ('collapse',)
        }),
        (_('File Options'), {
            'fields': ('upload_to',),
            'classes': ('collapse',)
        }),
        (_('Choice Options'), {
            'fields': ('choices',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(CodeGenerationHistory)
class CodeGenerationHistoryAdmin(admin.ModelAdmin):
    """
    Admin configuration for CodeGenerationHistory model.
    """
    list_display = ('project', 'user', 'status', 'generate_full', 'generate_frontend', 'execution_time', 'created_at')
    list_filter = ('status', 'generate_full', 'generate_frontend', 'created_at')
    search_fields = ('project__name', 'user__email', 'error_message')
    readonly_fields = ('created_at', 'updated_at', 'execution_time', 'generated_files_display')
    
    fieldsets = (
        (_('Basic Info'), {
            'fields': ('project', 'user', 'status')
        }),
        (_('Generation Options'), {
            'fields': ('generate_full', 'generate_frontend', 'output_directory'),
        }),
        (_('Results'), {
            'fields': ('execution_time', 'generated_files_display', 'error_message'),
            'classes': ('collapse',)
        }),
        (_('Configuration Snapshot'), {
            'fields': ('config_snapshot',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def generated_files_display(self, obj):
        if obj.generated_files:
            return '\n'.join(obj.generated_files)
        return '-'
    generated_files_display.short_description = _('Generated Files')
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False


@admin.register(ModelTemplate)
class ModelTemplateAdmin(admin.ModelAdmin):
    """
    Admin configuration for ModelTemplate model.
    """
    list_display = ('name', 'category', 'usage_count', 'is_active', 'is_featured', 'created_at')
    list_filter = ('category', 'is_active', 'is_featured', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at', 'usage_count')
    
    fieldsets = (
        (_('Basic Info'), {
            'fields': ('name', 'description', 'category', 'created_by')
        }),
        (_('Status'), {
            'fields': ('is_active', 'is_featured'),
        }),
        (_('Template Configuration'), {
            'fields': ('template_config',),
            'classes': ('collapse',)
        }),
        (_('Media'), {
            'fields': ('preview_image',),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('usage_count',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(FieldPreset)
class FieldPresetAdmin(admin.ModelAdmin):
    """
    Admin configuration for FieldPreset model.
    """
    list_display = ('name', 'usage_count', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description', 'tags')
    readonly_fields = ('created_at', 'updated_at', 'usage_count')
    
    fieldsets = (
        (_('Basic Info'), {
            'fields': ('name', 'description', 'created_by')
        }),
        (_('Status'), {
            'fields': ('is_active',),
        }),
        (_('Fields Configuration'), {
            'fields': ('fields_config',),
            'classes': ('collapse',)
        }),
        (_('Tags'), {
            'fields': ('tags',),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('usage_count',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
