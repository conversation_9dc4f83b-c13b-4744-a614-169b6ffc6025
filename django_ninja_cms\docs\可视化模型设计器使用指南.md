# 可视化模型设计器使用指南

## 概述

可视化模型设计器是一个基于 Web 界面的 Django 模型设计和代码生成工具，旨在替代传统的基于 YAML 配置文件的命令行方式，提供更直观、更便捷的模型设计体验。

## 主要功能

### 🎨 可视化模型编辑器
- **拖拽式设计**: 通过直观的 Web 界面设计 Django 模型
- **实时预览**: 实时显示模型结构和字段信息
- **关系可视化**: 自动显示模型间的外键、多对多关系

### 🔧 字段管理
- **完整字段类型支持**: 支持所有 Django 字段类型
- **字段属性配置**: null、blank、unique、help_text 等
- **关系字段配置**: 外键、多对多、一对一关系设置
- **字段预设**: 常用字段组合的快速添加

### 📦 模板系统
- **预定义模板**: 博客、电商、CMS 等常用模型模板
- **一键应用**: 快速创建基于模板的项目
- **自定义模板**: 支持创建和分享自定义模板

### 🚀 代码生成
- **一键生成**: 自动生成完整的 Django 应用代码
- **多种选项**: 支持生成模型、API、Admin、前端组件
- **历史记录**: 记录每次代码生成的详细信息

### 📄 配置管理
- **YAML 导入导出**: 兼容现有的 YAML 配置格式
- **版本控制**: 支持配置的版本管理
- **团队协作**: 便于多人协作开发

## 安装和配置

### 1. 应用已集成

可视化模型设计器已经集成到项目中，位于 `apps/model_designer` 目录。

### 2. 数据库迁移

首次使用需要创建数据库表：

```bash
# 创建迁移文件
python manage.py makemigrations model_designer

# 应用迁移
python manage.py migrate
```

### 3. 创建初始数据

运行管理命令创建初始模板和预设：

```bash
python manage.py create_initial_templates
```

这将创建以下初始数据：
- 博客系统模板
- 电商系统模板  
- CMS系统模板
- 常用字段预设（时间戳、SEO、状态控制等）

## 使用方法

### 1. 访问界面

启动开发服务器后，可以通过以下地址访问：

- **仪表板**: `http://localhost:8000/model-designer/dashboard/`
- **设计器**: `http://localhost:8000/model-designer/`

### 2. 创建项目

1. 点击"新建项目"按钮
2. 填写项目信息：
   - 项目名称
   - Django 应用名称
   - 项目描述
3. 点击"创建"完成项目创建

### 3. 设计模型

#### 添加模型
1. 选择项目后，点击"添加模型"
2. 填写模型信息：
   - 模型名称
   - 显示名称
   - 模型描述
3. 模型将出现在画布上

#### 添加字段
1. 选择模型
2. 在字段管理面板中添加字段
3. 配置字段属性：
   - 字段类型
   - 基本选项（null、blank、unique等）
   - 关系配置（针对外键字段）
   - 验证器和帮助文本

#### 使用字段预设
1. 在字段预设面板中选择预设
2. 点击"应用"将预设字段添加到模型
3. 可根据需要调整字段配置

### 4. 模型布局

- **拖拽移动**: 直接拖拽模型卡片调整位置
- **自动保存**: 位置变更会自动保存到服务器
- **关系显示**: 自动显示模型间的关系线

### 5. 配置导入导出

#### 导出配置
1. 选择项目
2. 点击"导出配置"按钮
3. 下载生成的 YAML 文件

#### 导入配置
1. 点击"导入配置"按钮
2. 粘贴 YAML 配置内容
3. 点击"导入"创建新项目

### 6. 代码生成

1. 选择项目
2. 点击"生成代码"按钮
3. 选择生成选项：
   - **完整功能**: 生成模型、API、Admin、权限等
   - **前端组件**: 生成 Vue.js 前端组件
   - **输出目录**: 指定代码输出位置
4. 查看生成历史和结果

## API 接口

### 认证

所有 API 接口都需要用户认证。使用 JWT Token 进行身份验证：

```bash
# 获取 Token
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 使用 Token
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/model-designer/projects/
```

### 主要端点

#### 项目管理
- `GET /api/model-designer/projects/` - 获取项目列表
- `POST /api/model-designer/projects/` - 创建项目
- `GET /api/model-designer/projects/{id}/` - 获取项目详情
- `PUT /api/model-designer/projects/{id}/` - 更新项目
- `DELETE /api/model-designer/projects/{id}/` - 删除项目

#### 模型设计
- `GET /api/model-designer/projects/{id}/models/` - 获取项目模型列表
- `POST /api/model-designer/projects/{id}/models/` - 创建模型
- `PUT /api/model-designer/models/{id}/` - 更新模型
- `DELETE /api/model-designer/models/{id}/` - 删除模型

#### 字段设计
- `GET /api/model-designer/models/{id}/fields/` - 获取模型字段列表
- `POST /api/model-designer/models/{id}/fields/` - 创建字段
- `PUT /api/model-designer/fields/{id}/` - 更新字段
- `DELETE /api/model-designer/fields/{id}/` - 删除字段

#### 代码生成
- `POST /api/model-designer/projects/{id}/generate/` - 生成代码
- `GET /api/model-designer/projects/{id}/generation-history/` - 获取生成历史

#### 配置管理
- `POST /api/model-designer/import-yaml/` - 导入 YAML 配置
- `GET /api/model-designer/projects/{id}/export-yaml/` - 导出 YAML 配置

### API 文档

完整的 API 文档可以通过以下地址访问：
- Swagger UI: `http://localhost:8000/docs/`
- OpenAPI JSON: `http://localhost:8000/api/openapi.json`

## 管理后台

### 访问管理后台

通过 Django Admin 可以管理所有数据：

```
http://localhost:8000/admin/
```

### 管理功能

- **模型项目**: 查看和管理所有项目
- **模型设计**: 管理模型和字段设计
- **代码生成历史**: 查看生成记录和结果
- **模板管理**: 管理预定义模板
- **字段预设**: 管理字段预设

## 故障排除

### 常见问题

#### 1. 迁移失败
```bash
# 重置迁移
python manage.py migrate model_designer zero
python manage.py makemigrations model_designer
python manage.py migrate
```

#### 2. 静态文件问题
```bash
# 收集静态文件
python manage.py collectstatic
```

#### 3. 权限问题
确保用户已登录且有相应权限。

#### 4. API 调用失败
检查：
- JWT Token 是否有效
- 请求头是否正确
- 用户是否有相应权限

### 日志查看

查看应用日志：
```bash
tail -f logs/django.log
```

## 扩展开发

### 添加新字段类型

1. 在 `models.py` 中的 `FIELD_TYPE_CHOICES` 添加新类型
2. 在前端界面中添加对应的处理逻辑
3. 更新代码生成逻辑

### 添加新模板

1. 通过管理后台或 API 创建新模板
2. 配置模板的 JSON 结构
3. 设置模板分类和描述

### 自定义代码生成

修改 `services.py` 中的 `CodeGenerationService` 类，自定义代码生成逻辑。

## 技术架构

### 后端技术栈
- **Django**: Web 框架
- **Django Ninja**: API 框架
- **Pydantic**: 数据验证
- **PostgreSQL/SQLite**: 数据库

### 前端技术栈
- **Vue.js 3**: 前端框架
- **Element Plus**: UI 组件库
- **Axios**: HTTP 客户端

### 核心组件
- **模型管理**: 项目、模型、字段的 CRUD 操作
- **可视化引擎**: 拖拽和关系图显示
- **代码生成器**: 基于模板的代码生成
- **配置引擎**: YAML 格式的配置导入导出

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础可视化模型设计功能
- ✅ 完整的 API 接口
- ✅ 模板和预设系统
- ✅ 代码生成功能
- ✅ YAML 配置兼容

### 计划功能
- 🔄 模型关系图可视化
- 🔄 实时代码预览
- 🔄 团队协作功能
- 🔄 版本控制集成
- 🔄 更多模板和预设

## 支持

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查项目的 GitHub Issues
3. 联系开发团队

---

*最后更新: 2024年*
