"""
Core API endpoints for health checks and system information.
"""

import platform
import sys
from datetime import datetime
from django.conf import settings
from django.db import connection
from django.core.cache import cache
from django.http import HttpRequest
from ninja import Router

core_router = Router()


@core_router.get("/health", auth=None)
def health_check(request: HttpRequest):
    """
    Health check endpoint for monitoring.
    """
    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"

    # Check cache
    try:
        cache.set("health_check", "test", 10)
        cache_value = cache.get("health_check")
        cache_status = "healthy" if cache_value == "test" else "unhealthy"
    except Exception as e:
        cache_status = f"unhealthy: {str(e)}"

    return {
        "status": (
            "healthy"
            if db_status == "healthy" and cache_status == "healthy"
            else "unhealthy"
        ),
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": db_status,
            "cache": cache_status,
        },
        "version": getattr(settings, "API_VERSION", "1.0.0"),
    }


@core_router.get("/info", auth=None)
def system_info(request: HttpRequest):
    """
    System information endpoint.
    """
    return {
        "application": {
            "name": getattr(settings, "API_TITLE", "Django Ninja Template"),
            "version": getattr(settings, "API_VERSION", "1.0.0"),
            "description": getattr(settings, "API_DESCRIPTION", ""),
            "environment": getattr(settings, "ENVIRONMENT", "unknown"),
        },
        "system": {
            "python_version": sys.version,
            "platform": platform.platform(),
            "architecture": platform.architecture()[0],
        },
        "timestamp": datetime.now().isoformat(),
    }


@core_router.get("/ping", auth=None)
def ping(request: HttpRequest):
    """
    Simple ping endpoint.
    """
    return {"message": "pong", "timestamp": datetime.now().isoformat()}
