"""
Core API endpoints for health checks and system information.
"""

import platform
import sys
from datetime import datetime
from django.conf import settings
from django.db import connection
from django.core.cache import cache
from django.http import HttpRequest
from ninja import Router

core_router = Router()


@core_router.get("/health", auth=None)
def health_check(request: HttpRequest):
    """
    Health check endpoint for monitoring.
    """
    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"

    # Check cache
    try:
        cache.set("health_check", "test", 10)
        cache_value = cache.get("health_check")
        cache_status = "healthy" if cache_value == "test" else "unhealthy"
    except Exception as e:
        cache_status = f"unhealthy: {str(e)}"

    return {
        "status": (
            "healthy"
            if db_status == "healthy" and cache_status == "healthy"
            else "unhealthy"
        ),
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": db_status,
            "cache": cache_status,
        },
        "version": getattr(settings, "API_VERSION", "1.0.0"),
    }


@core_router.get("/info", auth=None)
def system_info(request: HttpRequest):
    """
    System information endpoint.
    """
    return {
        "application": {
            "name": getattr(settings, "API_TITLE", "Django Ninja Template"),
            "version": getattr(settings, "API_VERSION", "1.0.0"),
            "description": getattr(settings, "API_DESCRIPTION", ""),
            "environment": getattr(settings, "ENVIRONMENT", "unknown"),
        },
        "system": {
            "python_version": sys.version,
            "platform": platform.platform(),
            "architecture": platform.architecture()[0],
        },
        "timestamp": datetime.now().isoformat(),
    }


@core_router.get("/ping", auth=None)
def ping(request: HttpRequest):
    """
    Simple ping endpoint.
    """
    return {"message": "pong", "timestamp": datetime.now().isoformat()}


# 可视化代码生成工具 API

from typing import List
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from ninja import Query
from .models import (
    ModelProject,
    ModelDesign,
    FieldDesign,
    CodeGenerationHistory,
    ModelTemplate,
    FieldPreset,
)
from .schemas import (
    ModelProjectCreateSchema,
    ModelProjectUpdateSchema,
    ModelProjectSchema,
    ModelProjectListSchema,
    ModelDesignCreateSchema,
    ModelDesignUpdateSchema,
    ModelDesignSchema,
    FieldDesignCreateSchema,
    FieldDesignUpdateSchema,
    FieldDesignSchema,
    CodeGenerationRequestSchema,
    CodeGenerationHistorySchema,
    YamlImportSchema,
    YamlExportSchema,
    ExistingModelSchema,
    ModelTemplateSchema,
    FieldPresetSchema,
    MessageSchema,
    ErrorSchema,
)
from .services import ModelProjectService, CodeGenerationService


# 模型项目管理 API
@core_router.get("/model-projects", response=List[ModelProjectListSchema])
def list_model_projects(request):
    """获取模型项目列表"""
    projects = ModelProject.objects.filter(owner=request.user, is_active=True)
    return [
        {**project.__dict__, "models_count": project.get_models_count()}
        for project in projects
    ]


@core_router.post("/model-projects", response=ModelProjectSchema)
def create_model_project(request, data: ModelProjectCreateSchema):
    """创建模型项目"""
    try:
        project = ModelProjectService.create_project(
            name=data.name,
            app_name=data.app_name,
            owner=request.user,
            description=data.description,
        )
        return {**project.__dict__, "models_count": project.get_models_count()}
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@core_router.get("/model-projects/{project_id}", response=ModelProjectSchema)
def get_model_project(request, project_id: int):
    """获取模型项目详情"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    return {**project.__dict__, "models_count": project.get_models_count()}


@core_router.put("/model-projects/{project_id}", response=ModelProjectSchema)
def update_model_project(request, project_id: int, data: ModelProjectUpdateSchema):
    """更新模型项目"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)

    project.save()
    return {**project.__dict__, "models_count": project.get_models_count()}


@core_router.delete("/model-projects/{project_id}", response=MessageSchema)
def delete_model_project(request, project_id: int):
    """删除模型项目"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    project.delete()
    return {"message": "项目删除成功"}


# 模型设计管理 API
@core_router.get(
    "/model-projects/{project_id}/models", response=List[ModelDesignSchema]
)
def list_model_designs(request, project_id: int):
    """获取项目的模型设计列表"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    models = project.model_designs.all()
    return [
        {**model.__dict__, "fields_count": model.get_fields_count()} for model in models
    ]


@core_router.post("/model-projects/{project_id}/models", response=ModelDesignSchema)
def create_model_design(request, project_id: int, data: ModelDesignCreateSchema):
    """创建模型设计"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)

    model_design = ModelDesign.objects.create(project=project, **data.dict())
    return {**model_design.__dict__, "fields_count": model_design.get_fields_count()}


@core_router.get("/model-designs/{model_id}", response=ModelDesignSchema)
def get_model_design(request, model_id: int):
    """获取模型设计详情"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )
    return {**model_design.__dict__, "fields_count": model_design.get_fields_count()}


@core_router.put("/model-designs/{model_id}", response=ModelDesignSchema)
def update_model_design(request, model_id: int, data: ModelDesignUpdateSchema):
    """更新模型设计"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(model_design, field, value)

    model_design.save()
    return {**model_design.__dict__, "fields_count": model_design.get_fields_count()}


@core_router.delete("/model-designs/{model_id}", response=MessageSchema)
def delete_model_design(request, model_id: int):
    """删除模型设计"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )
    model_design.delete()
    return {"message": "模型删除成功"}


# 字段设计管理 API
@core_router.get("/model-designs/{model_id}/fields", response=List[FieldDesignSchema])
def list_field_designs(request, model_id: int):
    """获取模型的字段设计列表"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )
    fields = model_design.field_designs.all()
    return [field.__dict__ for field in fields]


@core_router.post("/model-designs/{model_id}/fields", response=FieldDesignSchema)
def create_field_design(request, model_id: int, data: FieldDesignCreateSchema):
    """创建字段设计"""
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )

    field_design = FieldDesign.objects.create(model_design=model_design, **data.dict())
    return field_design.__dict__


@core_router.get("/field-designs/{field_id}", response=FieldDesignSchema)
def get_field_design(request, field_id: int):
    """获取字段设计详情"""
    field_design = get_object_or_404(
        FieldDesign, id=field_id, model_design__project__owner=request.user
    )
    return field_design.__dict__


@core_router.put("/field-designs/{field_id}", response=FieldDesignSchema)
def update_field_design(request, field_id: int, data: FieldDesignUpdateSchema):
    """更新字段设计"""
    field_design = get_object_or_404(
        FieldDesign, id=field_id, model_design__project__owner=request.user
    )

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(field_design, field, value)

    field_design.save()
    return field_design.__dict__


@core_router.delete("/field-designs/{field_id}", response=MessageSchema)
def delete_field_design(request, field_id: int):
    """删除字段设计"""
    field_design = get_object_or_404(
        FieldDesign, id=field_id, model_design__project__owner=request.user
    )
    field_design.delete()
    return {"message": "字段删除成功"}


# YAML 导入导出 API
@core_router.post("/model-projects/import-yaml", response=ModelProjectSchema)
def import_from_yaml(request, data: YamlImportSchema):
    """从 YAML 配置导入项目"""
    try:
        project = ModelProjectService.import_from_yaml(data.yaml_content, request.user)
        return {**project.__dict__, "models_count": project.get_models_count()}
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@core_router.get("/model-projects/{project_id}/export-yaml", response=YamlExportSchema)
def export_to_yaml(request, project_id: int):
    """导出项目为 YAML 配置"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    yaml_content = ModelProjectService.export_project_to_yaml(project)
    return {"yaml_content": yaml_content}


# 代码生成 API
@core_router.post(
    "/model-projects/{project_id}/generate", response=CodeGenerationHistorySchema
)
def generate_code(request, project_id: int, data: CodeGenerationRequestSchema):
    """生成代码"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)

    options = data.dict()
    history = CodeGenerationService.generate_code(project, request.user, options)
    return history.__dict__


@core_router.get(
    "/model-projects/{project_id}/generation-history",
    response=List[CodeGenerationHistorySchema],
)
def list_generation_history(request, project_id: int):
    """获取代码生成历史"""
    project = get_object_or_404(ModelProject, id=project_id, owner=request.user)
    history = project.generation_history.all()[:20]  # 最近20条记录
    return [h.__dict__ for h in history]


@core_router.get(
    "/generation-history/{history_id}", response=CodeGenerationHistorySchema
)
def get_generation_history(request, history_id: int):
    """获取代码生成历史详情"""
    history = get_object_or_404(CodeGenerationHistory, id=history_id, user=request.user)
    return history.__dict__


# 现有模型信息 API
@core_router.get("/existing-models", response=List[ExistingModelSchema])
def list_existing_models(request):
    """获取现有的 Django 模型信息"""
    models_info = ModelProjectService.get_existing_models()
    return models_info


# 模板和预设 API
@core_router.get("/model-templates", response=List[ModelTemplateSchema])
def list_model_templates(request, category: str = Query(None)):
    """获取模型模板列表"""
    templates = ModelTemplate.objects.filter(is_active=True)
    if category:
        templates = templates.filter(category=category)
    return [t.__dict__ for t in templates]


@core_router.get("/model-templates/{template_id}", response=ModelTemplateSchema)
def get_model_template(request, template_id: int):
    """获取模型模板详情"""
    template = get_object_or_404(ModelTemplate, id=template_id, is_active=True)
    return template.__dict__


@core_router.post("/model-templates/{template_id}/use", response=ModelProjectSchema)
def use_model_template(request, template_id: int):
    """使用模型模板创建项目"""
    template = get_object_or_404(ModelTemplate, id=template_id, is_active=True)

    try:
        # 增加使用次数
        template.increment_usage()

        # 从模板配置创建项目
        config = template.template_config
        import yaml

        yaml_content = yaml.dump(config, default_flow_style=False, allow_unicode=True)

        project = ModelProjectService.import_from_yaml(yaml_content, request.user)
        project.name = f"{template.name}_project"
        project.save()

        return {**project.__dict__, "models_count": project.get_models_count()}
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@core_router.get("/field-presets", response=List[FieldPresetSchema])
def list_field_presets(request, tags: str = Query(None)):
    """获取字段预设列表"""
    presets = FieldPreset.objects.filter(is_active=True)
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        for tag in tag_list:
            presets = presets.filter(tags__contains=tag)
    return [p.__dict__ for p in presets]


@core_router.get("/field-presets/{preset_id}", response=FieldPresetSchema)
def get_field_preset(request, preset_id: int):
    """获取字段预设详情"""
    preset = get_object_or_404(FieldPreset, id=preset_id, is_active=True)
    return preset.__dict__


@core_router.post("/field-presets/{preset_id}/use")
def use_field_preset(request, preset_id: int, model_id: int):
    """使用字段预设添加字段到模型"""
    preset = get_object_or_404(FieldPreset, id=preset_id, is_active=True)
    model_design = get_object_or_404(
        ModelDesign, id=model_id, project__owner=request.user
    )

    try:
        # 增加使用次数
        preset.increment_usage()

        # 添加预设字段到模型
        created_fields = []
        for field_config in preset.fields_config:
            field_design = FieldDesign.objects.create(
                model_design=model_design, **field_config
            )
            created_fields.append(field_design.__dict__)

        return {
            "message": f"成功添加 {len(created_fields)} 个字段",
            "fields": created_fields,
        }
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


# 字段类型和选项 API
@core_router.get("/field-types")
def get_field_types(request):
    """获取支持的字段类型"""
    from .models import FieldDesign

    return {
        "field_types": [
            {"value": choice[0], "label": choice[1]}
            for choice in FieldDesign.FIELD_TYPE_CHOICES
        ],
        "on_delete_choices": [
            {"value": choice[0], "label": choice[1]}
            for choice in FieldDesign.ON_DELETE_CHOICES
        ],
    }
